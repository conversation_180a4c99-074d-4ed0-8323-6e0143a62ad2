<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl">
    <!-- Slider Items -->
    <div class="lg:col-span-2 space-y-8">
        <div>
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-800">Слайдове</h3>
                <button id="add-slide-button" class="px-3 py-1.5 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm flex items-center !rounded-button">
                    <div class="w-4 h-4 flex items-center justify-center mr-1">
                        <i class="ri-add-line"></i>
                    </div>
                    <span>Добави слайд</span>
                </button>
            </div>
            
            <div id="slides-container" class="space-y-4">
                {% if slider_items %}
                    {% for slide in slider_items %}
                    <div class="slide-item border border-gray-200 rounded-lg overflow-hidden" data-slide-id="{{ slide.id }}">
                        <div class="relative">
                            <img src="{{ slide.image_url }}" alt="Slider image" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                <button class="preview-slide p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Преглед">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-eye-line"></i>
                                    </div>
                                </button>
                                <button class="edit-slide p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Редактиране">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-image-edit-line"></i>
                                    </div>
                                </button>
                                <button class="delete-slide p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Изтриване">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>
                            </div>
                        </div>
                        <div class="p-4 bg-white">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 class="font-medium text-gray-800 mb-1">{{ slide.title }}</h4>
                                    <p class="text-sm text-gray-500">{{ slide.subtitle }}</p>
                                </div>
                                <div class="flex items-center">
                                    <button class="move-up p-1.5 text-gray-400 hover:text-gray-600" title="Нагоре">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-arrow-up-line"></i>
                                        </div>
                                    </button>
                                    <button class="move-down p-1.5 text-gray-400 hover:text-gray-600" title="Надолу">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-arrow-down-line"></i>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-12">
                        <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-gray-400">
                            <i class="ri-image-add-line ri-3x"></i>
                        </div>
                        <h3 class="text-gray-600 font-medium mb-2">Все още няма добавени слайдове</h3>
                        <p class="text-sm text-gray-500 mb-4">Добавете първия слайд като натиснете бутона по-горе</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Slider Settings -->
    <div class="lg:col-span-1">
        <div class="sticky top-6">
            <h3 class="text-lg font-medium text-gray-800 mb-4">Настройки на слайдера</h3>
            <form id="slider-settings-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Автоматично превъртане</label>
                    <div class="flex items-center">
                        <label class="toggle-switch mr-2">
                            <input type="checkbox" name="auto_play" {{ slider_settings.auto_play ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="text-sm text-gray-700">{{ slider_settings.auto_play ? 'Активирано' : 'Деактивирано' }}</span>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Интервал на превъртане (секунди)</label>
                    <div class="flex items-center space-x-4">
                        <input type="range" min="1" max="10" value="{{ slider_settings.interval|default(5) }}" class="slider" id="slider-interval" name="interval">
                        <span class="text-sm text-gray-700" id="slider-interval-value">{{ slider_settings.interval|default(5) }} сек.</span>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Анимация при преход</label>
                    <div class="relative">
                        <select name="animation" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm appearance-none pr-10">
                            <option value="fade" {{ slider_settings.animation == 'fade' ? 'selected' : '' }}>Избледняване</option>
                            <option value="slide" {{ slider_settings.animation == 'slide' ? 'selected' : '' }}>Плъзгане</option>
                            <option value="zoom" {{ slider_settings.animation == 'zoom' ? 'selected' : '' }}>Увеличаване</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                                <i class="ri-arrow-down-s-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Показване на навигационни елементи</label>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="checkbox" id="show-arrows" name="show_arrows" {{ slider_settings.show_arrows ? 'checked' : '' }} class="mr-2">
                            <label for="show-arrows" class="text-sm text-gray-700">Стрелки</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="show-dots" name="show_dots" {{ slider_settings.show_dots ? 'checked' : '' }} class="mr-2">
                            <label for="show-dots" class="text-sm text-gray-700">Точки</label>
                        </div>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Мобилен изглед</label>
                    <div class="relative">
                        <select name="mobile_view" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm appearance-none pr-10">
                            <option value="show" {{ slider_settings.mobile_view == 'show' ? 'selected' : '' }}>Показване на слайдера</option>
                            <option value="hide" {{ slider_settings.mobile_view == 'hide' ? 'selected' : '' }}>Скриване на слайдера</option>
                            <option value="single" {{ slider_settings.mobile_view == 'single' ? 'selected' : '' }}>Показване само на първия слайд</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                                <i class="ri-arrow-down-s-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm !rounded-button">
                        Запази настройките
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Slide Modal -->
<div id="slide-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-800" id="slide-modal-title">Добавяне на слайд</h3>
            <button class="close-slide-modal text-gray-400 hover:text-gray-600">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-close-line"></i>
                </div>
            </button>
        </div>
        <form id="slide-form" class="p-4">
            <input type="hidden" name="slide_id" id="slide-id">
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Изображение</label>
                    <div id="slide-image-container" class="w-full">
                        <div id="slide-image-placeholder" class="border border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" style="min-height: 200px;">
                            <div class="flex items-center space-x-2 mb-2">
                                <button type="button" data-action="select-slide-image" class="w-10 h-10 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                                    <i class="ri-folder-image-line ri-2x"></i>
                                </button>
                            </div>
                            <p class="text-sm text-gray-400 text-center">Няма изображение</p>
                            <p class="text-xs text-gray-400 text-center mt-1">Кликнете за да изберете изображение</p>
                        </div>
                    </div>
                    <input type="hidden" name="image" id="slide-image-input">
                    <p class="text-xs text-gray-500 mt-2">Препоръчителен размер: 1536x512px. Поддържани формати: JPG, PNG, GIF</p>
                </div>

                <!-- CSS стилове за изображението в модала -->
                <style>
                #slide-image-container img {
                    width: 100% !important;
                    height: auto !important;
                    max-height: 400px;
                    object-fit: cover;
                    border-radius: 8px;
                }

                #slide-image-container .slide-image-preview {
                    width: 100% !important;
                    display: block;
                }
                </style>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Заглавие</label>
                    <input type="text" name="title" id="slide-title" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете заглавие на слайда">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Подзаглавие</label>
                    <textarea name="subtitle" id="slide-subtitle" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете подзаглавие или описание"></textarea>
                </div>
                
                <div>
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">Бутони</label>
                        <button type="button" id="add-slide-button-btn" class="px-2 py-1 text-primary hover:bg-primary/5 rounded text-sm flex items-center">
                            <div class="w-4 h-4 flex items-center justify-center mr-1">
                                <i class="ri-add-line"></i>
                            </div>
                            <span>Добави бутон</span>
                        </button>
                    </div>
                    <div id="slide-buttons-container" class="space-y-3">
                        <!-- Buttons will be added here -->
                    </div>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="status" id="slide-status" class="mr-2">
                    <label for="slide-status" class="text-sm text-gray-700">Активен слайд</label>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" class="close-slide-modal px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors text-sm !rounded-button">Отказ</button>
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm !rounded-button">Запази</button>
            </div>
        </form>
    </div>
</div>

<!-- Button Template -->
<template id="slide-button-template">
    <div class="slide-button-item border-2 border-dashed border-blue-200 bg-blue-50/30 rounded-lg p-4 mb-4">
        <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-medium text-gray-700">Бутон</h4>
            <button type="button" class="remove-slide-button p-1 text-red-500 hover:text-red-600 hover:bg-red-50 rounded">
                <div class="w-4 h-4 flex items-center justify-center">
                    <i class="ri-delete-bin-line"></i>
                </div>
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
            <div>
                <label class="block text-xs font-medium text-gray-600 mb-1">Текст на бутона</label>
                <input type="text" class="button-text w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете текст">
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-600 mb-1">Тип действие</label>
                <select class="button-action w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                    <option value="url">URL адрес</option>
                    <option value="page">Избор на страница</option>
                    <option value="javascript">JavaScript код</option>
                </select>
            </div>
        </div>

        <!-- Dynamic fields container -->
        <div class="button-dynamic-fields">
            <!-- URL field (default) -->
            <div class="button-url-field">
                <label class="block text-xs font-medium text-gray-600 mb-1">URL адрес</label>
                <input type="text" class="button-value w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="https://example.com">
            </div>

            <!-- Page selection field (hidden by default) -->
            <div class="button-page-field hidden">
                <label class="block text-xs font-medium text-gray-600 mb-1">Избрани страници</label>
                <div class="button-selected-pages mb-2">
                    <!-- Selected pages will appear here -->
                </div>
                <div class="relative">
                    <input type="text" class="button-page-search w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Търсене на страници...">
                    <div class="button-page-dropdown absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-b shadow-lg max-h-48 overflow-y-auto hidden z-10">
                        <!-- Search results will appear here -->
                    </div>
                </div>
            </div>

            <!-- JavaScript field (hidden by default) -->
            <div class="button-js-field hidden">
                <label class="block text-xs font-medium text-gray-600 mb-1">JavaScript код</label>
                <textarea class="button-js-code w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" rows="3" placeholder="console.log('Button clicked');"></textarea>
            </div>
        </div>
    </div>
</template>
