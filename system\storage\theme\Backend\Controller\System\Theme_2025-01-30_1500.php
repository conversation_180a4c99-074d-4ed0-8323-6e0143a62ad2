<?php

namespace Theme25\Backend\Controller\System;

/**
 * Главен контролер за настройки на темата
 *
 * Този контролер управлява всички операции свързани с настройки на темата в административната част.
 * Следва sub-controller архитектурата на темата и предоставя методи за различни типове настройки.
 *
 * @package Theme25\Backend\Controller\System
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Theme extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'system/theme');
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $scripts = [
            'theme.js',
            'theme-slider.js',
            'theme-banners.js',
            'theme-pages.js',
            'image-manager.js',
            'AdvancedRichTextEditor.js'
        ];

        $this->addBackendScriptWithVersion($scripts, 'footer');
    }

    /**
     * Основен метод - показва настройките на темата с табове
     */
    public function index() {
        $this->setTitle('Настройки на тема');

        // Инициализиране на данните
        $this->initAdminData();

        try {
            $subController = $this->setBackendSubController('System/Theme/Index', $this);

            // Подготовка на данните
            $subController->prepareData();

            // Добавяне на основни данни ако липсват
            if (!isset($this->data['current_tab'])) {
                $this->setData('current_tab', $this->requestGet('tab', 'slider'));
            }

        } catch (Exception $e) {
            // Ако има грешка, задаваме минимални данни
            $this->setData([
                'current_tab' => 'slider',
                'tabs' => [
                    'slider' => 'Начален слайдер',
                    'banners' => 'Банери',
                    'pages' => 'Страници'
                ]
            ]);
        }

        if(!$this->isAjaxRequest()) {
            // Зареждане на JavaScript файлове специфични за този контролер
            $this->loadScripts();
            $this->getJSconfiguration();
        }

        $current_tab = ucfirst($this->requestGet('tab', 'slider'));

        $subController = $this->setBackendSubController('System/Theme/' . $current_tab, $this);

        // Подготовка на данните
        $subController->prepareData();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('system/theme');
    }

    /**
     * AJAX метод за автодопълване
     */
    public function autocomplete() {
        $json = [];

        if ($this->requestGet('type')) {
            $type = $this->requestGet('type');

            // Динамично зареждане на суб-контролер
            $sub_controller = $this->setBackendSubController('System/Theme/' . ucfirst($type) . 'Autocomplete', $this);

            if ($sub_controller && is_callable([$sub_controller, 'autocomplete'])) {
                $json = $sub_controller->autocomplete($this->requestGet());
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за запазване на настройки
     */
    public function save() {
        $json = ['success' => false, 'message' => ''];

        if ($this->isPostRequest()) {
            $tab = $this->requestPost('tab', 'slider');

            $subController = $this->setBackendSubController('System/Theme/' . ucfirst($tab), $this);

            if ($subController && is_callable([$subController, 'save'])) {
                $json = $subController->save($this->requestPost());
            } else {
                $json['message'] = 'Невалиден таб или метод за запазване.';
            }
        } else {
            $json['message'] = 'Невалидна заявка.';
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за изтриване на елементи
     */
    public function delete() {
        $json = ['success' => false, 'message' => ''];

        if ($this->isPostRequest()) {
            $tab = $this->requestPost('tab', 'slider');
            $id = $this->requestPost('id', 0);

            $subController = $this->setBackendSubController('System/Theme/' . ucfirst($tab), $this);

            if ($subController && is_callable([$subController, 'delete'])) {
                $json = $subController->delete($id);
            } else {
                $json['message'] = 'Невалиден таб или метод за изтриване.';
            }
        } else {
            $json['message'] = 'Невалидна заявка.';
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за промяна на реда на елементите
     */
    public function reorder() {
        $json = ['success' => false, 'message' => ''];

        if ($this->isPostRequest()) {
            $tab = $this->requestPost('tab', 'slider');
            $order = $this->requestPost('order', []);

            $subController = $this->setBackendSubController('System/Theme/' . ucfirst($tab), $this);

            if ($subController && is_callable([$subController, 'reorder'])) {
                $json = $subController->reorder($order);
            } else {
                $json['message'] = 'Невалиден таб или метод за пренареждане.';
            }
        } else {
            $json['message'] = 'Невалидна заявка.';
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява дали потребителят има права за достъп до този контролер
     */
    public function hasPermission($action = 'access') {
        return $this->user->hasPermission($action, 'system/theme');
    }
}
