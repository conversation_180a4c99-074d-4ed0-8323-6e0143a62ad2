/**
 * JavaScript функционалност за управление на слайдера в темата
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initThemeSlider();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за слайдера
            slider: {
                slideModal: null,
                currentSlideId: null,
                slides: [],
                slidesContainer: null,
                slideForm: null,
                sliderSettingsForm: null
            },

            /**
             * Инициализация на функционалността за слайдера
             */
            initThemeSlider: function() {
                this.initSliderElements();
                this.initSliderEvents();
                this.initSliderSortable();
            },

            /**
             * Инициализация на елементите за слайдера
             */
            initSliderElements: function() {
                this.slider.slideModal = document.getElementById('slide-modal');
                this.slider.slidesContainer = document.getElementById('slides-container');
                this.slider.slideForm = document.getElementById('slide-form');
                this.slider.sliderSettingsForm = document.getElementById('slider-settings-form');
            },

            /**
             * Свързване на събития за слайдера
             */
            initSliderEvents: function() {
                const self = this;

                // Бутон за добавяне на слайд
                const addSlideBtn = document.getElementById('add-slide-button');
                if (addSlideBtn) {
                    addSlideBtn.addEventListener('click', function() {
                        self.openSlideModal();
                    });
                }

                // Затваряне на модала
                document.querySelectorAll('.close-slide-modal').forEach(btn => {
                    btn.addEventListener('click', function() {
                        self.closeSlideModal();
                    });
                });

                // Клик извън модала
                if (this.slider.slideModal) {
                    this.slider.slideModal.addEventListener('click', function(e) {
                        if (e.target === self.slider.slideModal) {
                            self.closeSlideModal();
                        }
                    });
                }

                // Форма за слайд
                if (this.slider.slideForm) {
                    this.slider.slideForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveSlide();
                    });
                }

                // Форма за настройки на слайдера
                if (this.slider.sliderSettingsForm) {
                    this.slider.sliderSettingsForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveSliderSettings();
                    });
                }

                // Бутони в слайдовете
                this.initSlideButtons();

                // Добавяне на бутони в слайда
                const addButtonBtn = document.getElementById('add-slide-button-btn');
                if (addButtonBtn) {
                    addButtonBtn.addEventListener('click', function() {
                        self.addSlideButton();
                    });
                }

                // Избор на изображение
                this.initSliderImageSelection();

                // Slider за интервал
                this.initSliderInterval();
            },

            /**
             * Свързване на бутоните в слайдовете
             */
            initSlideButtons: function() {
                const self = this;

                // Редактиране на слайд
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.edit-slide')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        const slideId = slideItem.getAttribute('data-slide-id');
                        self.editSlide(slideId);
                    }
                });

                // Изтриване на слайд
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.delete-slide')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        const slideId = slideItem.getAttribute('data-slide-id');
                        self.deleteSlide(slideId);
                    }
                });

                // Преглед на слайд
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.preview-slide')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        const img = slideItem.querySelector('img');
                        self.previewSlide(img.src);
                    }
                });

                // Преместване нагоре/надолу
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.move-up')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        self.moveSlide(slideItem, 'up');
                    }
                    if (e.target.closest('.move-down')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        self.moveSlide(slideItem, 'down');
                    }
                });
            },

            /**
             * Свързване на избора на изображение за слайдера
             */
            initSliderImageSelection: function() {
                const self = this;

                document.addEventListener('click', function(e) {
                    if (e.target.closest('[data-action="select-slide-image"]')) {
                        e.preventDefault();
                        self.openSliderImageManager('slide');
                    }
                });
            },

            /**
             * Свързване на slider за интервал
             */
            initSliderInterval: function() {
                const slider = document.getElementById('slider-interval');
                const valueDisplay = document.getElementById('slider-interval-value');

                if (slider && valueDisplay) {
                    slider.addEventListener('input', function() {
                        valueDisplay.textContent = `${this.value} сек.`;
                    });
                }
            },

            /**
             * Отваряне на модала за слайд
             */
            openSlideModal: function(slideData) {
                slideData = slideData || null;
                this.slider.currentSlideId = slideData ? slideData.id : null;

                // Задаване на заглавието
                const title = document.getElementById('slide-modal-title');
                if (title) {
                    title.textContent = slideData ? 'Редактиране на слайд' : 'Добавяне на слайд';
                }

                // Попълване на формата
                if (slideData) {
                    this.populateSlideForm(slideData);
                } else {
                    this.resetSlideForm();
                }

                // Показване на модала
                if (this.slider.slideModal) {
                    this.slider.slideModal.classList.remove('hidden');
                }
            },

            /**
             * Затваряне на модала за слайд
             */
            closeSlideModal: function() {
                if (this.slider.slideModal) {
                    this.slider.slideModal.classList.add('hidden');
                }
                this.slider.currentSlideId = null;
                this.resetSlideForm();
            },

            /**
             * Попълване на формата за слайд
             */
            populateSlideForm: function(slideData) {
                const slideIdField = document.getElementById('slide-id');
                const slideTitleField = document.getElementById('slide-title');
                const slideSubtitleField = document.getElementById('slide-subtitle');
                const slideImageField = document.getElementById('slide-image-input');
                const slideStatusField = document.getElementById('slide-status');

                if (slideIdField) slideIdField.value = slideData.id || '';
                if (slideTitleField) slideTitleField.value = slideData.title || '';
                if (slideSubtitleField) slideSubtitleField.value = slideData.subtitle || '';
                if (slideImageField) slideImageField.value = slideData.image || '';
                if (slideStatusField) slideStatusField.checked = slideData.status || false;

                // Показване на изображението
                if (slideData.image_url) {
                    this.updateSlideImagePreview(slideData.image_url);
                }

                // Добавяне на бутоните
                const buttonsContainer = document.getElementById('slide-buttons-container');
                if (buttonsContainer) {
                    buttonsContainer.innerHTML = '';

                    if (slideData.buttons && slideData.buttons.length > 0) {
                        slideData.buttons.forEach(button => {
                            this.addSlideButton(button);
                        });
                    }
                }
            },

            /**
             * Нулиране на формата за слайд
             */
            resetSlideForm: function() {
                if (this.slider.slideForm) {
                    this.slider.slideForm.reset();
                }

                // Нулиране на изображението
                this.resetSlideImagePreview();

                // Изчистване на бутоните
                const buttonsContainer = document.getElementById('slide-buttons-container');
                if (buttonsContainer) {
                    buttonsContainer.innerHTML = '';
                }
            },

            /**
             * Обновяване на прегледа на изображението за слайд
             */
            updateSlideImagePreview: function(imageUrl) {
                const container = document.getElementById('slide-image-container');
                if (container) {
                    container.innerHTML = `
                        <div class="relative group" style="width: 192px; height: auto; min-height: 96px;">
                            <div class="aspect-auto rounded-lg overflow-hidden border border-gray-200">
                                <img src="${imageUrl}" alt="Slide image" class="w-full h-full object-contain">
                            </div>
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Промени изображение" data-action="select-slide-image">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-folder-image-line"></i>
                                    </div>
                                </button>
                                <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахни изображение" onclick="BackendModule.resetSlideImagePreview()">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>
                            </div>
                        </div>
                    `;
                }
            },

            /**
             * Нулиране на прегледа на изображението за слайд
             */
            resetSlideImagePreview: function() {
                const container = document.getElementById('slide-image-container');
                if (container) {
                    container.innerHTML = `
                        <div id="slide-image-placeholder" class="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" style="width: 192px; height: 96px;">
                            <div class="flex items-center space-x-2 mb-2">
                                <button type="button" data-action="select-slide-image" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                                    <i class="ri-folder-image-line ri-lg"></i>
                                </button>
                            </div>
                            <p class="text-xs text-gray-400 text-center">Няма изображение</p>
                        </div>
                    `;
                }

                // Нулиране на скритото поле
                const imageInput = document.getElementById('slide-image-input');
                if (imageInput) {
                    imageInput.value = '';
                }
            },

            /**
             * Добавяне на бутон в слайда
             */
            addSlideButton: function(buttonData) {
                buttonData = buttonData || null;
                const template = document.getElementById('slide-button-template');
                const container = document.getElementById('slide-buttons-container');

                if (template && container) {
                    const buttonElement = template.content.cloneNode(true);

                    if (buttonData) {
                        const textField = buttonElement.querySelector('.button-text');
                        const actionField = buttonElement.querySelector('.button-action');
                        const valueField = buttonElement.querySelector('.button-value');

                        if (textField) textField.value = buttonData.text || '';
                        if (actionField) actionField.value = buttonData.action || 'url';
                        if (valueField) valueField.value = buttonData.value || '';
                    }

                    // Добавяне на event listener за премахване
                    const removeBtn = buttonElement.querySelector('.remove-slide-button');
                    if (removeBtn) {
                        removeBtn.addEventListener('click', function(e) {
                            const buttonItem = e.target.closest('.slide-button-item');
                            if (buttonItem) {
                                buttonItem.remove();
                            }
                        });
                    }

                    container.appendChild(buttonElement);
                }
            },

            /**
             * Запазване на слайд
             */
            saveSlide: function() {
                const self = this;
                const formData = new FormData(this.slider.slideForm);

                // Добавяне на бутоните
                const buttons = [];
                document.querySelectorAll('.slide-button-item').forEach(item => {
                    const textField = item.querySelector('.button-text');
                    const actionField = item.querySelector('.button-action');
                    const valueField = item.querySelector('.button-value');

                    const text = textField ? textField.value : '';
                    const action = actionField ? actionField.value : '';
                    const value = valueField ? valueField.value : '';

                    if (text && value) {
                        buttons.push({ text, action, value });
                    }
                });

                formData.append('buttons', JSON.stringify(buttons));
                formData.append('action', this.slider.currentSlideId ? 'edit' : 'add');
                formData.append('tab', 'slider');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                        self.closeSlideModal();

                        // Презареждане на страницата за обновяване на списъка
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Save slide error:', error);
                    self.showAlert('error', 'Възникна грешка при запазването на слайда.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Запазване на настройките на слайдера
             */
            saveSliderSettings: function() {
                const self = this;
                const formData = new FormData(this.slider.sliderSettingsForm);
                formData.append('action', 'settings');
                formData.append('tab', 'slider');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Save settings error:', error);
                    self.showAlert('error', 'Възникна грешка при запазването на настройките.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Отваряне на image manager за слайдера
             */
            openSliderImageManager: function(type) {
                const self = this;
                const targetContainer = document.getElementById('slide-image-container');

                BackendModule.openImageManager({
                    singleSelection: true,
                    startDirectory: '',
                    target: targetContainer,
                    callback: function(selectedImages, target) {
                        if (selectedImages && selectedImages.length > 0 && target && type === 'slide') {
                            const selectedImage = selectedImages[0];
                            const imageInput = document.getElementById('slide-image-input');
                            const imgElement = target.querySelector('img');

                            if (imageInput) {
                                imageInput.value = selectedImage.path;
                            }

                            if (imgElement) {
                                imgElement.src = selectedImage.thumb;
                            }

                            self.updateSlideImagePreview(selectedImage.thumb);
                            self.showAlert('success', 'Изображението е успешно избрано.');
                        }
                    }
                });
            },

            /**
             * Инициализация на sortable за слайдовете
             */
            initSliderSortable: function() {
                const self = this;
                const slidesContainer = document.getElementById('slides-container');

                if (!slidesContainer) return;

                let draggedElement = null;
                let draggedIndex = null;

                // Добавяне на drag атрибути към слайдовете
                const slides = slidesContainer.querySelectorAll('.slide-item');
                slides.forEach((slide, index) => {
                    slide.draggable = true;
                    slide.style.cursor = 'move';

                    // Добавяне на drag handle визуално
                    const dragHandle = document.createElement('div');
                    dragHandle.className = 'absolute top-2 left-2 p-1 bg-white/80 rounded text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity';
                    dragHandle.innerHTML = '<i class="ri-drag-move-line"></i>';
                    dragHandle.style.pointerEvents = 'none';

                    const imageContainer = slide.querySelector('.relative');
                    if (imageContainer) {
                        imageContainer.classList.add('group');
                        imageContainer.appendChild(dragHandle);
                    }
                });

                // Drag events
                slidesContainer.addEventListener('dragstart', function(e) {
                    if (e.target.closest('.slide-item')) {
                        draggedElement = e.target.closest('.slide-item');
                        draggedIndex = Array.from(slidesContainer.children).indexOf(draggedElement);
                        draggedElement.style.opacity = '0.5';
                        e.dataTransfer.effectAllowed = 'move';
                    }
                });

                slidesContainer.addEventListener('dragend', function(e) {
                    if (draggedElement) {
                        draggedElement.style.opacity = '';
                        draggedElement = null;
                        draggedIndex = null;
                    }
                });

                slidesContainer.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                });

                slidesContainer.addEventListener('drop', function(e) {
                    e.preventDefault();

                    if (!draggedElement) return;

                    const dropTarget = e.target.closest('.slide-item');
                    if (!dropTarget || dropTarget === draggedElement) return;

                    const dropIndex = Array.from(slidesContainer.children).indexOf(dropTarget);

                    // Преместване на елемента в DOM
                    if (draggedIndex < dropIndex) {
                        dropTarget.parentNode.insertBefore(draggedElement, dropTarget.nextSibling);
                    } else {
                        dropTarget.parentNode.insertBefore(draggedElement, dropTarget);
                    }

                    // Изпращане на новия ред към сървъра
                    self.updateSliderOrder();
                });
            },

            /**
             * Обновяване на реда на слайдовете след drag & drop
             */
            updateSliderOrder: function() {
                const self = this;
                const slidesContainer = document.getElementById('slides-container');

                if (!slidesContainer) return;

                const slideIds = [];
                slidesContainer.querySelectorAll('.slide-item').forEach(slide => {
                    const slideId = slide.getAttribute('data-slide-id');
                    if (slideId) {
                        slideIds.push(slideId);
                    }
                });

                if (slideIds.length === 0) return;

                const formData = new FormData();
                formData.append('order', JSON.stringify(slideIds));
                formData.append('tab', 'slider');
                formData.append('user_token', this.getUserToken());

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/reorder', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', 'Редът на слайдовете е променен успешно.');
                    } else {
                        self.showAlert('error', result.message || 'Грешка при пренареждане на слайдовете.');
                        // Презареждане на страницата при грешка
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                })
                .catch(error => {
                    console.error('Reorder error:', error);
                    self.showAlert('error', 'Възникна грешка при пренареждането.');
                    // Презареждане на страницата при грешка
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                });
            },

            /**
             * Редактиране на слайд
             */
            editSlide: function(slideId) {
                const self = this;

                if (!slideId) {
                    self.showAlert('error', 'Невалиден ID на слайд.');
                    return;
                }

                // Показване на loading индикатор
                this.showThemeLoading();

                // AJAX заявка за получаване на данните на слайда
                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/autocomplete&type=slide&id=' + slideId + '&user_token=' + this.getUserToken(), {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(slideData => {
                    if (slideData && slideData.id) {
                        // Отваряне на модала с данните на слайда
                        self.openSlideModal(slideData);
                    } else {
                        self.showAlert('error', 'Слайдът не е намерен или възникна грешка при зареждането.');
                    }
                })
                .catch(error => {
                    console.error('Edit slide error:', error);
                    self.showAlert('error', 'Възникна грешка при зареждането на данните на слайда.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Изтриване на слайд
             */
            deleteSlide: function(slideId) {
                const self = this;

                if (!slideId) {
                    self.showAlert('error', 'Невалиден ID на слайд.');
                    return;
                }

                // Потвърждение за изтриване
                if (!confirm('Сигурни ли сте, че искате да изтриете този слайд?')) {
                    return;
                }

                // Показване на loading индикатор
                this.showThemeLoading();

                const formData = new FormData();
                formData.append('id', slideId);
                formData.append('tab', 'slider');
                formData.append('user_token', this.getUserToken());

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/delete', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message || 'Слайдът е изтрит успешно.');

                        // Премахване на слайда от DOM
                        const slideElement = document.querySelector(`[data-slide-id="${slideId}"]`);
                        if (slideElement) {
                            slideElement.remove();
                        }

                        // Проверка дали има останали слайдове
                        const remainingSlides = document.querySelectorAll('.slide-item');
                        if (remainingSlides.length === 0) {
                            // Показване на съобщение за липса на слайдове
                            const slidesContainer = document.getElementById('slides-container');
                            if (slidesContainer) {
                                slidesContainer.innerHTML = `
                                    <div class="text-center py-12">
                                        <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-gray-400">
                                            <i class="ri-image-add-line ri-3x"></i>
                                        </div>
                                        <h3 class="text-gray-600 font-medium mb-2">Все още няма добавени слайдове</h3>
                                        <p class="text-sm text-gray-500 mb-4">Добавете първия слайд като натиснете бутона по-горе</p>
                                    </div>
                                `;
                            }
                        }
                    } else {
                        self.showAlert('error', result.message || 'Възникна грешка при изтриването на слайда.');
                    }
                })
                .catch(error => {
                    console.error('Delete slide error:', error);
                    self.showAlert('error', 'Възникна грешка при изтриването на слайда.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Преглед на слайд
             */
            previewSlide: function(imageSrc) {
                if (!imageSrc) {
                    this.showAlert('error', 'Няма изображение за преглед.');
                    return;
                }

                // Създаване на модал за преглед ако не съществува
                let previewModal = document.getElementById('slide-preview-modal');
                if (!previewModal) {
                    previewModal = document.createElement('div');
                    previewModal.id = 'slide-preview-modal';
                    previewModal.className = 'fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center hidden';
                    previewModal.innerHTML = `
                        <div class="relative max-w-4xl max-h-[90vh] mx-4">
                            <button class="absolute top-4 right-4 p-2 bg-white rounded-full text-gray-700 hover:text-gray-900 z-10" onclick="BackendModule.closeSlidePreview()">
                                <div class="w-6 h-6 flex items-center justify-center">
                                    <i class="ri-close-line"></i>
                                </div>
                            </button>
                            <img id="slide-preview-image" src="" alt="Slide preview" class="max-w-full max-h-full object-contain rounded-lg">
                        </div>
                    `;
                    document.body.appendChild(previewModal);

                    // Затваряне при клик извън изображението
                    previewModal.addEventListener('click', function(e) {
                        if (e.target === previewModal) {
                            BackendModule.closeSlidePreview();
                        }
                    });
                }

                // Задаване на изображението и показване на модала
                const previewImage = document.getElementById('slide-preview-image');
                if (previewImage) {
                    previewImage.src = imageSrc;
                    previewModal.classList.remove('hidden');
                }
            },

            /**
             * Затваряне на модала за преглед на слайд
             */
            closeSlidePreview: function() {
                const previewModal = document.getElementById('slide-preview-modal');
                if (previewModal) {
                    previewModal.classList.add('hidden');
                }
            },

            /**
             * Преместване на слайд
             */
            moveSlide: function(slideItem, direction) {
                if (!slideItem || !direction) {
                    this.showAlert('error', 'Невалидни параметри за преместване.');
                    return;
                }

                const slidesContainer = document.getElementById('slides-container');
                if (!slidesContainer) return;

                const slides = Array.from(slidesContainer.querySelectorAll('.slide-item'));
                const currentIndex = slides.indexOf(slideItem);

                if (currentIndex === -1) {
                    this.showAlert('error', 'Слайдът не е намерен.');
                    return;
                }

                let targetIndex;

                if (direction === 'up') {
                    if (currentIndex === 0) {
                        this.showAlert('info', 'Слайдът вече е на първо място.');
                        return;
                    }
                    targetIndex = currentIndex - 1;
                } else if (direction === 'down') {
                    if (currentIndex === slides.length - 1) {
                        this.showAlert('info', 'Слайдът вече е на последно място.');
                        return;
                    }
                    targetIndex = currentIndex + 1;
                } else {
                    this.showAlert('error', 'Невалидна посока за преместване.');
                    return;
                }

                // Преместване в DOM
                const targetSlide = slides[targetIndex];
                if (direction === 'up') {
                    slidesContainer.insertBefore(slideItem, targetSlide);
                } else {
                    slidesContainer.insertBefore(slideItem, targetSlide.nextSibling);
                }

                // Обновяване на реда в базата данни
                this.updateSliderOrder();
            }
        });
    }

})();
