<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-800">Страници</h3>
        <button id="add-page-button" class="px-3 py-1.5 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm flex items-center !rounded-button">
            <div class="w-4 h-4 flex items-center justify-center mr-1">
                <i class="ri-add-line"></i>
            </div>
            <span>Добави нова страница</span>
        </button>
    </div>

    {% if page_items %}
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all-pages" class="rounded border-gray-300">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Име на страница
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Статус
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Действия
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for page in page_items %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="selected_pages[]" value="{{ page.information_id }}" class="page-checkbox rounded border-gray-300">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ page.title }}</div>
                                        <div class="text-sm text-gray-500">ID: {{ page.information_id }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if page.status %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Активна
                                    </span>
                                {% else %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                        Неактивна
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <button class="edit-page text-primary hover:text-primary/80 transition-colors" data-page-id="{{ page.information_id }}" title="Редактиране">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-edit-line"></i>
                                        </div>
                                    </button>
                                    <button class="delete-page text-red-500 hover:text-red-600 transition-colors" data-page-id="{{ page.information_id }}" title="Изтриване">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-delete-bin-line"></i>
                                        </div>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <select id="bulk-action" class="px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                    <option value="">Изберете действие...</option>
                    <option value="delete">Изтрий избраните</option>
                    <option value="activate">Активирай избраните</option>
                    <option value="deactivate">Деактивирай избраните</option>
                </select>
                <button id="apply-bulk-action" class="px-4 py-2 bg-gray-600 text-white rounded-button hover:bg-gray-700 transition-colors text-sm !rounded-button" disabled>
                    Приложи
                </button>
            </div>
            
            {% if pagination %}
                <div class="pagination-wrapper">
                    {{ pagination }}
                </div>
            {% endif %}
        </div>
    {% else %}
        <div class="text-center py-12">
            <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-gray-400">
                <i class="ri-file-text-line ri-3x"></i>
            </div>
            <h3 class="text-gray-600 font-medium mb-2">Все още няма създадени страници</h3>
            <p class="text-sm text-gray-500 mb-4">Добавете първата страница като натиснете бутона по-горе</p>
        </div>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-page-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg w-full max-w-md mx-4 p-6">
        <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-red-500">
                <i class="ri-delete-bin-line ri-2x"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-800 mb-2">Изтриване на страница</h3>
            <p class="text-gray-600 mb-6">Сигурни ли сте, че искате да изтриете тази страница? Това действие не може да бъде отменено.</p>
            <div class="flex justify-center space-x-3">
                <button id="cancel-delete-page" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors text-sm !rounded-button">Отказ</button>
                <button id="confirm-delete-page" class="px-4 py-2 bg-red-500 text-white rounded-button hover:bg-red-600 transition-colors text-sm !rounded-button">Изтрий</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div id="bulk-delete-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg w-full max-w-md mx-4 p-6">
        <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-red-500">
                <i class="ri-delete-bin-line ri-2x"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-800 mb-2">Изтриване на страници</h3>
            <p class="text-gray-600 mb-6">Сигурни ли сте, че искате да изтриете избраните страници? Това действие не може да бъде отменено.</p>
            <div class="flex justify-center space-x-3">
                <button id="cancel-bulk-delete" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors text-sm !rounded-button">Отказ</button>
                <button id="confirm-bulk-delete" class="px-4 py-2 bg-red-500 text-white rounded-button hover:bg-red-600 transition-colors text-sm !rounded-button">Изтрий всички</button>
            </div>
        </div>
    </div>
</div>

<!-- Page Edit Modal -->
<div id="page-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-800" id="page-modal-title">Редактиране на страница</h3>
            <button class="close-page-modal text-gray-400 hover:text-gray-600">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-close-line"></i>
                </div>
            </button>
        </div>
        <form id="page-form" class="p-6">
            <input type="hidden" name="information_id" id="page-id">
            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Заглавие на страницата</label>
                        <input type="text" name="title" id="page-title" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете заглавие" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">SEO URL</label>
                        <input type="text" name="keyword" id="page-keyword" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="seo-friendly-url">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Съдържание</label>
                    <div id="page-content-editor" class="border border-gray-300 rounded" style="min-height: 300px;">
                        <textarea name="description" id="page-description" class="w-full h-64 px-3 py-2 border-0 resize-none focus:outline-none text-sm" placeholder="Въведете съдържанието на страницата..."></textarea>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Meta заглавие</label>
                        <input type="text" name="meta_title" id="page-meta-title" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Meta заглавие за SEO">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Meta ключови думи</label>
                        <input type="text" name="meta_keyword" id="page-meta-keyword" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="ключова дума, друга ключова дума">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Meta описание</label>
                    <textarea name="meta_description" id="page-meta-description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Кратко описание на страницата за търсачките"></textarea>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" name="status" id="page-status" class="mr-2">
                    <label for="page-status" class="text-sm text-gray-700">Активна страница</label>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                <button type="button" class="close-page-modal px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors text-sm !rounded-button">Отказ</button>
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm !rounded-button">Запази</button>
            </div>
        </form>
    </div>
</div>
