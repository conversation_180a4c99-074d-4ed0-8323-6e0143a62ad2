<?php

namespace Theme25\Backend\Controller\System;

/**
 * Главен контролер за настройки на темата
 *
 * Този контролер управлява всички операции свързани с настройки на темата в административната част.
 * Следва sub-controller архитектурата на темата и предоставя методи за различни типове настройки.
 *
 * @package Theme25\Backend\Controller\System
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Theme extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'system/theme');
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $scripts = [
            'theme.js',
            'theme-slider.js',
            'theme-banners.js',
            'theme-pages.js',
            'image-manager.js',
            'AdvancedRichTextEditor.js'
        ];

        $this->addBackendScriptWithVersion($scripts, 'footer');
    }

    /**
     * Подготовка на JavaScript конфигурация
     */
    protected function getJSconfiguration() {
        // Подготовка на всички AJAX URL-и за JavaScript
        $ajax_urls = $this->prepareAllAjaxUrls();

        // Задаване на данните за изгледа
        $ajax_urls = json_encode($ajax_urls, JSON_UNESCAPED_UNICODE);
        $base_url = $this->getAdminLink('system/theme');
        $current_tab = $this->requestGet('tab', 'slider');

        $js_config = "
            <!-- JavaScript Configuration -->
            <script>
            window.themeConfig = {
                currentTab: '{$current_tab}',
                baseUrl: '{$base_url}',
                ajaxUrls: {$ajax_urls}
            };
            </script>
            ";

        $this->setData('js_config', $js_config);
    }

    /**
     * Подготовка на всички AJAX URL-и
     */
    private function prepareAllAjaxUrls() {
        return [
            'save' => $this->getAdminLink('system/theme/save'),
            'delete' => $this->getAdminLink('system/theme/delete'),
            'reorder' => $this->getAdminLink('system/theme/reorder'),
            'autocomplete' => $this->getAdminLink('system/theme/autocomplete')
        ];
    }

    /**
     * Основен метод - показва настройките на темата с табове
     */
    public function index() {
        $this->setTitle('Настройки на тема');

        // Инициализиране на данните
        $this->initAdminData();

        try {
            $subController = $this->setBackendSubController('System/Theme/Index', $this);

            // Подготовка на данните
            $subController->prepareData();

            // Добавяне на основни данни ако липсват
            if (!isset($this->data['current_tab'])) {
                $this->setData('current_tab', $this->requestGet('tab', 'slider'));
            }

        } catch (Exception $e) {
            // Ако има грешка, задаваме минимални данни
            $this->setData([
                'current_tab' => 'slider',
                'tabs' => [
                    'slider' => 'Начален слайдер',
                    'banners' => 'Банери',
                    'pages' => 'Страници'
                ]
            ]);
        }

        if(!$this->isAjaxRequest()) {
            // Зареждане на JavaScript файлове специфични за този контролер
            $this->loadScripts();
            $this->getJSconfiguration();
        }

        // За AJAX заявки, зареждаме само съдържанието на конкретния таб
        // if ($this->isAjaxRequest()) {
        //     $current_tab = ucfirst($this->requestGet('tab', 'slider'));
        //     $subController = $this->setBackendSubController('System/Theme/' . $current_tab, $this);

        //     // Подготовка на данните
        //     $subController->prepareData();

        //     // Рендиране само на таб шаблона
        //     $this->renderTemplateWithDataAndOutput('system/tabs/theme-' . strtolower($current_tab));
        //     return;
        // }

        $current_tab = ucfirst($this->requestGet('tab', 'slider'));

        $subController = $this->setBackendSubController('System/Theme/' . $current_tab, $this);

        // Подготовка на данните
        $subController->prepareData();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('system/theme');
    }

    /**
     * AJAX метод за автодопълване
     */
    public function autocomplete() {
        $json = [];

        if ($this->requestGet('type')) {
            $type = $this->requestGet('type');

            // Динамично зареждане на суб-контролер
            $sub_controller = $this->setBackendSubController('System/Theme/' . ucfirst($type) . 'Autocomplete', $this);

            if ($sub_controller && is_callable([$sub_controller, 'autocomplete'])) {
                $json = $sub_controller->autocomplete($this->requestGet());
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за запазване на настройки
     */
    public function save() {
        $json = ['success' => false, 'message' => ''];

        if ($this->isPostRequest()) {
            $tab = $this->requestPost('tab', 'slider');

            $subController = $this->setBackendSubController('System/Theme/' . ucfirst($tab), $this);

            if ($subController && is_callable([$subController, 'save'])) {
                $json = $subController->save($this->requestPost());
            } else {
                $json['message'] = 'Невалиден таб или метод за запазване.';
            }
        } else {
            $json['message'] = 'Невалидна заявка.';
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за изтриване на елементи
     */
    public function delete() {
        $json = ['success' => false, 'message' => ''];

        if ($this->isPostRequest()) {
            $tab = $this->requestPost('tab', 'slider');
            $id = $this->requestPost('id', 0);

            $subController = $this->setBackendSubController('System/Theme/' . ucfirst($tab), $this);

            if ($subController && is_callable([$subController, 'delete'])) {
                $json = $subController->delete($id);
            } else {
                $json['message'] = 'Невалиден таб или метод за изтриване.';
            }
        } else {
            $json['message'] = 'Невалидна заявка.';
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за промяна на реда на елементите
     */
    public function reorder() {
        $json = ['success' => false, 'message' => ''];

        if ($this->isPostRequest()) {
            $tab = $this->requestPost('tab', 'slider');
            $order = $this->requestPost('order', []);

            $subController = $this->setBackendSubController('System/Theme/' . ucfirst($tab), $this);

            if ($subController && is_callable([$subController, 'reorder'])) {
                $json = $subController->reorder($order);
            } else {
                $json['message'] = 'Невалиден таб или метод за пренареждане.';
            }
        } else {
            $json['message'] = 'Невалидна заявка.';
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Оразмеряване на изображение
     */
    public function resize_image() {
        $json = ['success' => false, 'message' => ''];

        try {
            $imagePath = $this->requestPost('image', '');
            $width = (int)$this->requestPost('width', 0);
            $height = (int)$this->requestPost('height', 0);

            if (empty($imagePath)) {
                $json['message'] = 'Не е посочен път към изображение.';
                $this->outputJson($json);
                return;
            }

            if ($width <= 0 || $height <= 0) {
                $json['message'] = 'Невалидни размери за изображението.';
                $this->outputJson($json);
                return;
            }

            // Използваме ImageEditor класа за оразмеряване
            $imageEditor = new \Theme25\ImageEditor();

            // Генериране на име за оразмереното изображение
            $pathInfo = pathinfo($imagePath);
            $resizedFileName = $pathInfo['filename'] . '_' . $width . 'x' . $height . '.' . $pathInfo['extension'];
            $resizedPath = $pathInfo['dirname'] . '/' . $resizedFileName;

            // Оразмеряване на изображението
            $result = $imageEditor->resize($imagePath, $resizedPath, $width, $height);

            if ($result) {
                $json = [
                    'success' => true,
                    'message' => 'Изображението е успешно оразмерено.',
                    'resized_path' => $resizedPath,
                    'resized_url' => \Theme25\Data::getInstance()->getImageWebUrl() . $resizedPath
                ];
            } else {
                $json['message'] = 'Грешка при оразмеряване на изображението.';
            }

        } catch (Exception $e) {
            $json['message'] = 'Грешка при оразмеряване: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }

    /**
     * Проверява дали потребителят има права за достъп до този контролер
     */
    public function hasPermission($action = 'access') {
        return $this->user->hasPermission($action, 'system/theme');
    }
}
