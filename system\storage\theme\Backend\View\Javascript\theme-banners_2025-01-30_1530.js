/**
 * JavaScript функционалност за управление на банерите в темата
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initThemeBanners();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за банерите
            banners: {
                bannerModal: null,
                previewModal: null,
                currentBannerId: null,
                bannerForm: null,
                bannersContainer: null
            },

            /**
             * Инициализация на функционалността за банерите
             */
            initThemeBanners: function() {
                this.initBannerElements();
                this.initBannerEvents();
            },

            /**
             * Инициализация на елементите за банерите
             */
            initBannerElements: function() {
                this.banners.bannerModal = document.getElementById('banner-modal');
                this.banners.previewModal = document.getElementById('banner-preview-modal');
                this.banners.bannerForm = document.getElementById('banner-form');
                this.banners.bannersContainer = document.getElementById('banners-container');
            },

            /**
             * Свързване на събития за банерите
             */
            initBannerEvents: function() {
                const self = this;

                // Бутон за добавяне на банер
                const addBannerBtn = document.getElementById('add-banner-button');
                if (addBannerBtn) {
                    addBannerBtn.addEventListener('click', function() {
                        self.openBannerModal();
                    });
                }

                // Затваряне на модалите
                document.querySelectorAll('.close-banner-modal').forEach(btn => {
                    btn.addEventListener('click', function() {
                        self.closeBannerModal();
                    });
                });

                document.querySelectorAll('.close-banner-preview').forEach(btn => {
                    btn.addEventListener('click', function() {
                        self.closeBannerPreviewModal();
                    });
                });

                // Клик извън модалите
                if (this.banners.bannerModal) {
                    this.banners.bannerModal.addEventListener('click', function(e) {
                        if (e.target === self.banners.bannerModal) {
                            self.closeBannerModal();
                        }
                    });
                }

                if (this.banners.previewModal) {
                    this.banners.previewModal.addEventListener('click', function(e) {
                        if (e.target === self.banners.previewModal) {
                            self.closeBannerPreviewModal();
                        }
                    });
                }

                // Форма за банер
                if (this.banners.bannerForm) {
                    this.banners.bannerForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveBanner();
                    });
                }

                // Бутони в банерите
                this.initBannerButtons();

                // Избор на изображение
                this.initBannerImageSelection();

                // Радио бутони за тип линк
                this.initBannerLinkTypeRadios();

                // Инициализация на page search
                this.initBannerPageSearch();

                // Инициализация на размери на банери
                this.initBannerSizeSelection();
            },

            /**
             * Свързване на бутоните в банерите
             */
            initBannerButtons: function() {
                const self = this;

                // Редактиране на банер
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.edit-banner')) {
                        e.preventDefault();
                        const bannerItem = e.target.closest('.banner-item');
                        const bannerId = bannerItem.getAttribute('data-banner-id');
                        self.editBanner(bannerId);
                    }
                });

                // Изтриване на банер
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.delete-banner')) {
                        e.preventDefault();
                        const bannerItem = e.target.closest('.banner-item');
                        const bannerId = bannerItem.getAttribute('data-banner-id');
                        self.deleteBanner(bannerId);
                    }
                });

                // Преглед на банер
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.preview-banner')) {
                        e.preventDefault();
                        const bannerItem = e.target.closest('.banner-item');
                        const img = bannerItem.querySelector('img');
                        self.previewBanner(img.src);
                    }
                });
            },

            /**
             * Свързване на избора на изображение за банери
             */
            initBannerImageSelection: function() {
                const self = this;

                document.addEventListener('click', function(e) {
                    if (e.target.closest('[data-action="select-banner-image"]')) {
                        e.preventDefault();
                        self.openBannerImageManager('banner');
                    }
                });
            },

            /**
             * Свързване на радио бутоните за тип линк
             */
            initBannerLinkTypeRadios: function() {
                const self = this;

                document.addEventListener('change', function(e) {
                    if (e.target.name === 'link_type') {
                        self.toggleBannerLinkInputs(e.target.value);
                    }
                });
            },

            /**
             * Превключване на полетата за линк в банерите
             */
            toggleBannerLinkInputs: function(linkType) {
                const pageOption = document.querySelector('.banner-page-option');
                const urlOption = document.querySelector('.banner-url-option');
                const pageSearch = document.getElementById('banner-page-search');
                const linkInput = document.getElementById('banner-link');
                const pageIdInput = document.getElementById('banner-page-id');

                if (linkType === 'page') {
                    // Активиране на page опцията
                    if (pageSearch) {
                        pageSearch.disabled = false;
                        pageSearch.style.opacity = '1';
                    }
                    if (linkInput) {
                        linkInput.disabled = true;
                        linkInput.value = '';
                        linkInput.style.opacity = '0.5';
                    }
                } else if (linkType === 'url') {
                    // Активиране на URL опцията
                    if (pageSearch) {
                        pageSearch.disabled = true;
                        pageSearch.style.opacity = '0.5';
                    }
                    if (linkInput) {
                        linkInput.disabled = false;
                        linkInput.style.opacity = '1';
                    }
                    // Изчистване на избраната страница
                    if (pageIdInput) {
                        pageIdInput.value = '';
                    }
                    const selectedPagesContainer = document.querySelector('.banner-selected-pages');
                    if (selectedPagesContainer) {
                        selectedPagesContainer.innerHTML = '';
                    }
                }
            },

            /**
             * Инициализация на page search за банери
             */
            initBannerPageSearch: function() {
                const self = this;
                const searchInput = document.getElementById('banner-page-search');
                const dropdown = document.getElementById('banner-page-dropdown');

                if (!searchInput || !dropdown) return;

                let searchTimeout;

                searchInput.addEventListener('input', function(e) {
                    const query = e.target.value.trim();

                    clearTimeout(searchTimeout);

                    if (query.length < 2) {
                        dropdown.classList.add('hidden');
                        return;
                    }

                    searchTimeout = setTimeout(() => {
                        self.searchBannerPages(query, dropdown);
                    }, 300);
                });

                // Скриване на dropdown при клик извън него
                document.addEventListener('click', function(e) {
                    if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                        dropdown.classList.add('hidden');
                    }
                });
            },

            /**
             * Търсене на страници за банери
             */
            searchBannerPages: function(query, dropdown) {
                const self = this;

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/autocomplete&type=pages&q=' + encodeURIComponent(query) + '&user_token=' + this.getUserToken(), {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(pages => {
                    dropdown.innerHTML = '';

                    if (pages && pages.length > 0) {
                        pages.forEach(page => {
                            const pageItem = document.createElement('div');
                            pageItem.className = 'px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm';
                            pageItem.textContent = page.name;
                            pageItem.addEventListener('click', function() {
                                self.selectBannerPage(page);
                                dropdown.classList.add('hidden');
                                document.getElementById('banner-page-search').value = '';
                            });
                            dropdown.appendChild(pageItem);
                        });
                        dropdown.classList.remove('hidden');
                    } else {
                        dropdown.innerHTML = '<div class="px-3 py-2 text-sm text-gray-500">Няма намерени страници</div>';
                        dropdown.classList.remove('hidden');
                    }
                })
                .catch(error => {
                    console.error('Banner page search error:', error);
                    dropdown.classList.add('hidden');
                });
            },

            /**
             * Избор на страница за банер
             */
            selectBannerPage: function(page) {
                const selectedPagesContainer = document.querySelector('.banner-selected-pages');
                const pageIdInput = document.getElementById('banner-page-id');

                if (!selectedPagesContainer || !pageIdInput) return;

                // Премахване на предишни избори (само една страница може да бъде избрана)
                selectedPagesContainer.innerHTML = '';

                const pageTag = document.createElement('div');
                pageTag.className = 'inline-flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mb-2';
                pageTag.innerHTML = `
                    <span>${page.name}</span>
                    <button type="button" class="ml-1 text-blue-600 hover:text-blue-800" onclick="this.parentElement.remove(); document.getElementById('banner-page-id').value = '';">
                        <i class="ri-close-line"></i>
                    </button>
                `;

                selectedPagesContainer.appendChild(pageTag);
                pageIdInput.value = page.id;
            },

            /**
             * Инициализация на избор на размер за банери
             */
            initBannerSizeSelection: function() {
                const self = this;
                const sizeSelect = document.getElementById('banner-size');

                if (!sizeSelect) return;

                sizeSelect.addEventListener('change', function(e) {
                    const selectedSize = e.target.value;
                    const imageInput = document.getElementById('banner-image-input');

                    if (selectedSize !== 'custom' && imageInput && imageInput.value) {
                        // Автоматично оразмеряване на съществуващото изображение
                        const currentImage = {
                            path: imageInput.value,
                            url: imageInput.value // Ще се използва като fallback
                        };

                        self.resizeBannerImage(currentImage, function(resizedImageUrl) {
                            self.updateBannerImagePreview(resizedImageUrl);
                            self.showAlert('success', 'Изображението е автоматично оразмерено според новия размер.');
                        });
                    }
                });
            },

            /**
             * Отваряне на модала за банер
             */
            openBannerModal: function(bannerData) {
                bannerData = bannerData || null;
                this.banners.currentBannerId = bannerData ? bannerData.id : null;

                // Задаване на заглавието
                const title = document.getElementById('banner-modal-title');
                if (title) {
                    title.textContent = bannerData ? 'Редактиране на банер' : 'Добавяне на банер';
                }

                // Попълване на формата
                if (bannerData) {
                    this.populateBannerForm(bannerData);
                } else {
                    this.resetBannerForm();
                }

                // Зареждане на страниците
                this.loadBannerPages();

                // Показване на модала
                if (this.banners.bannerModal) {
                    this.banners.bannerModal.classList.remove('hidden');
                }
            },

            /**
             * Затваряне на модала за банер
             */
            closeBannerModal: function() {
                if (this.banners.bannerModal) {
                    this.banners.bannerModal.classList.add('hidden');
                }
                this.banners.currentBannerId = null;
                this.resetBannerForm();
            },

            /**
             * Затваряне на модала за преглед на банер
             */
            closeBannerPreviewModal: function() {
                if (this.banners.previewModal) {
                    this.banners.previewModal.classList.add('hidden');
                }
            },

            /**
             * Попълване на формата за банер
             */
            populateBannerForm: function(bannerData) {
                const bannerIdField = document.getElementById('banner-id');
                const bannerTitleField = document.getElementById('banner-title');
                const bannerStatusField = document.getElementById('banner-status');
                const bannerImageField = document.getElementById('banner-image-input');

                if (bannerIdField) bannerIdField.value = bannerData.id || '';
                if (bannerTitleField) bannerTitleField.value = bannerData.title || '';
                if (bannerStatusField) bannerStatusField.checked = bannerData.status || false;
                if (bannerImageField) bannerImageField.value = bannerData.image || '';

                // Показване на изображението
                if (bannerData.image_url) {
                    this.updateBannerImagePreview(bannerData.image_url);
                }

                // Настройка на линка
                if (bannerData.page_id && bannerData.page_name) {
                    const pageRadio = document.querySelector('input[name="link_type"][value="page"]');
                    if (pageRadio) pageRadio.checked = true;

                    // Добавяне на избраната страница в autocomplete
                    this.selectBannerPage({
                        id: bannerData.page_id,
                        name: bannerData.page_name
                    });

                    this.toggleBannerLinkInputs('page');
                } else if (bannerData.link) {
                    const urlRadio = document.querySelector('input[name="link_type"][value="url"]');
                    const linkInput = document.getElementById('banner-link');
                    if (urlRadio) urlRadio.checked = true;
                    if (linkInput) linkInput.value = bannerData.link;
                    this.toggleBannerLinkInputs('url');
                } else {
                    // По подразбиране избираме page опцията
                    const pageRadio = document.querySelector('input[name="link_type"][value="page"]');
                    if (pageRadio) pageRadio.checked = true;
                    this.toggleBannerLinkInputs('page');
                }
            },

            /**
             * Нулиране на формата за банер
             */
            resetBannerForm: function() {
                if (this.banners.bannerForm) {
                    this.banners.bannerForm.reset();
                }

                // Нулиране на изображението
                this.resetBannerImagePreview();

                // Изчистване на избраните страници
                const selectedPagesContainer = document.querySelector('.banner-selected-pages');
                if (selectedPagesContainer) {
                    selectedPagesContainer.innerHTML = '';
                }

                const pageIdInput = document.getElementById('banner-page-id');
                if (pageIdInput) {
                    pageIdInput.value = '';
                }

                // Настройка на първоначалното състояние на линковете
                this.toggleBannerLinkInputs('page');
            },

            /**
             * Обновяване на прегледа на изображението за банер
             */
            updateBannerImagePreview: function(imageUrl) {
                const container = document.getElementById('banner-image-container');
                if (container) {
                    container.innerHTML = `
                        <div class="relative group" style="width: 300px; height: auto; min-height: 150px;">
                            <div class="aspect-auto rounded-lg overflow-hidden border border-gray-200">
                                <img src="${imageUrl}" alt="Banner image" class="w-full h-full object-contain">
                            </div>
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Промени изображение" data-action="select-banner-image">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-folder-image-line"></i>
                                    </div>
                                </button>
                                <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахни изображение" onclick="BackendModule.resetBannerImagePreview()">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>
                            </div>
                        </div>
                    `;
                }
            },

            /**
             * Нулиране на прегледа на изображението за банер
             */
            resetBannerImagePreview: function() {
                const container = document.getElementById('banner-image-container');
                if (container) {
                    container.innerHTML = `
                        <div id="banner-image-placeholder" class="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" style="width: 300px; height: 150px;">
                            <div class="flex items-center space-x-2 mb-2">
                                <button type="button" data-action="select-banner-image" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                                    <i class="ri-folder-image-line ri-lg"></i>
                                </button>
                            </div>
                            <p class="text-xs text-gray-400 text-center">Няма изображение</p>
                        </div>
                    `;
                }

                // Нулиране на скритото поле
                const imageInput = document.getElementById('banner-image-input');
                if (imageInput) {
                    imageInput.value = '';
                }
            },

            /**
             * Зареждане на страниците за банерите
             */
            loadBannerPages: function() {
                const self = this;
                const userToken = this.getUserToken();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/autocomplete&type=pages&user_token=' + userToken, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(pages => {
                    const select = document.getElementById('banner-page-select');

                    if (select) {
                        // Изчистване на съществуващите опции (освен първата)
                        while (select.children.length > 1) {
                            select.removeChild(select.lastChild);
                        }

                        // Добавяне на новите опции
                        pages.forEach(page => {
                            const option = document.createElement('option');
                            option.value = page.id;
                            option.textContent = page.name;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Load pages error:', error);
                });
            },

            /**
             * Запазване на банер
             */
            saveBanner: function() {
                const self = this;
                const formData = new FormData(this.banners.bannerForm);
                formData.append('tab', 'banners');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                        self.closeBannerModal();

                        // Презареждане на страницата за обновяване на списъка
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Save banner error:', error);
                    self.showAlert('error', 'Възникна грешка при запазването на банера.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Изтриване на банер
             */
            deleteBanner: function(bannerId) {
                if (!confirm('Сигурни ли сте, че искате да изтриете този банер?')) {
                    return;
                }

                const self = this;
                const formData = new FormData();
                formData.append('id', bannerId);
                formData.append('tab', 'banners');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/delete', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);

                        // Премахване на банера от DOM
                        const bannerItem = document.querySelector(`[data-banner-id="${bannerId}"]`);
                        if (bannerItem) {
                            bannerItem.remove();
                        }
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Delete banner error:', error);
                    self.showAlert('error', 'Възникна грешка при изтриването на банера.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Преглед на банер
             */
            previewBanner: function(imageUrl) {
                const previewImage = document.getElementById('banner-preview-image');
                if (previewImage && this.banners.previewModal) {
                    previewImage.src = imageUrl;
                    this.banners.previewModal.classList.remove('hidden');
                }
            },

            /**
             * Отваряне на image manager за банери
             */
            openBannerImageManager: function(type) {
                const self = this;
                const targetContainer = document.getElementById('banner-image-container');

                BackendModule.openImageManager({
                    singleSelection: true,
                    startDirectory: '',
                    target: targetContainer,
                    callback: function(selectedImages, target) {
                        if (selectedImages && selectedImages.length > 0 && target && type === 'banner') {
                            const selectedImage = selectedImages[0];
                            const imageInput = document.getElementById('banner-image-input');

                            if (imageInput) {
                                // Използваме правилния path от selectedImage
                                const imagePath = selectedImage.path || selectedImage.image || selectedImage.src;
                                imageInput.value = imagePath;
                            }

                            // Автоматично оразмеряване според избрания размер
                            self.resizeBannerImage(selectedImage, function(resizedImageUrl) {
                                self.updateBannerImagePreview(resizedImageUrl);
                                self.showAlert('success', 'Изображението е успешно избрано и оразмерено.');
                            });
                        }
                    }
                });
            },

            /**
             * Автоматично оразмеряване на банер изображение
             */
            resizeBannerImage: function(selectedImage, callback) {
                const self = this;
                const sizeSelect = document.getElementById('banner-size');
                const selectedSize = sizeSelect ? sizeSelect.value : '300x250';

                if (selectedSize === 'custom') {
                    // За персонализиран размер използваме оригиналното изображение
                    const originalUrl = selectedImage.url || selectedImage.src;
                    callback(originalUrl);
                    return;
                }

                // Извличане на размерите
                const [width, height] = selectedSize.split('x').map(Number);

                if (!width || !height) {
                    // Ако размерите не са валидни, използваме оригиналното изображение
                    const originalUrl = selectedImage.url || selectedImage.src;
                    callback(originalUrl);
                    return;
                }

                // AJAX заявка за оразмеряване на изображението
                const imagePath = selectedImage.path || selectedImage.image || selectedImage.src;
                const formData = new FormData();
                formData.append('image', imagePath);
                formData.append('width', width);
                formData.append('height', height);
                formData.append('user_token', this.getUserToken());

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/resize_image', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success && result.resized_url) {
                        callback(result.resized_url);
                    } else {
                        // При грешка използваме оригиналното изображение
                        const originalUrl = selectedImage.url || selectedImage.src;
                        callback(originalUrl);
                        console.warn('Image resize failed, using original:', result.message);
                    }
                })
                .catch(error => {
                    console.error('Image resize error:', error);
                    // При грешка използваме оригиналното изображение
                    const originalUrl = selectedImage.url || selectedImage.src;
                    callback(originalUrl);
                });
            },

            /**
             * Редактиране на банер
             */
            editBanner: function(bannerId) {
                const self = this;

                if (!bannerId) {
                    self.showAlert('error', 'Невалиден ID на банер.');
                    return;
                }

                // Показване на loading индикатор
                this.showThemeLoading();

                // AJAX заявка за получаване на данните на банера
                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/autocomplete&type=banner&id=' + bannerId + '&user_token=' + this.getUserToken(), {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(bannerData => {
                    if (bannerData && bannerData.id) {
                        // Отваряне на модала с данните на банера
                        self.openBannerModal(bannerData);
                    } else {
                        self.showAlert('error', 'Банерът не е намерен или възникна грешка при зареждането.');
                    }
                })
                .catch(error => {
                    console.error('Edit banner error:', error);
                    self.showAlert('error', 'Възникна грешка при зареждането на данните на банера.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            }
        });
    }

})();
