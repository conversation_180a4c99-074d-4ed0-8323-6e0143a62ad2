/**
 * JavaScript функционалност за настройки на темата
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initTheme();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за темата
            theme: {
                currentTab: 'slider'
            },

            /**
             * Инициализация на функционалността за темата
             */
            initTheme: function() {
                this.initThemeTabSwitching();
                this.initThemeEvents();
            },

            /**
             * Инициализация на превключването между табове
             */
            initThemeTabSwitching: function() {
                const tabButtons = document.querySelectorAll('.tab-button');
                const tabContents = document.querySelectorAll('.tab-content');

                tabButtons.forEach((button) => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();

                        const tabName = button.getAttribute('data-tab');
                        if (tabName) {
                            this.switchThemeTab(tabName);
                        }
                    });
                });
            },

            /**
             * Превключване на таб
             */
            switchThemeTab: function(tabName) {
                // Премахване на активния клас от всички бутони
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                    btn.classList.add('text-gray-500');
                });

                // Скриване на всички табове
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });

                // Активиране на избрания таб
                const activeButton = document.querySelector(`[data-tab="${tabName}"]`);
                const activeContent = document.getElementById(`tab-${tabName}`);

                if (activeButton && activeContent) {
                    activeButton.classList.add('active');
                    activeButton.classList.remove('text-gray-500');
                    activeContent.classList.remove('hidden');

                    this.theme.currentTab = tabName;

                    // Обновяване на URL-а без презареждане
                    const url = new URL(window.location);
                    url.searchParams.set('tab', tabName);
                    window.history.pushState({}, '', url);
                }
            },

            /**
             * Свързване на общи събития за темата
             */
            initThemeEvents: function() {
                // Обработка на промени в URL-а
                window.addEventListener('popstate', () => {
                    const urlParams = new URLSearchParams(window.location.search);
                    const tab = urlParams.get('tab') || 'slider';
                    this.switchThemeTab(tab);
                });

                // Обработка на форми за запазване
                this.initThemeSaveForms();
            },

            /**
             * Свързване на форми за запазване
             */
            initThemeSaveForms: function() {
                // Общ обработчик за AJAX форми
                document.addEventListener('submit', (e) => {
                    const form = e.target;
                    if (form.classList.contains('ajax-form') || form.hasAttribute('data-ajax')) {
                        e.preventDefault();
                        this.submitThemeForm(form);
                    }
                });
            },

            /**
             * Изпращане на форма чрез AJAX
             */
            submitThemeForm: function(form) {
                const self = this;
                const formData = new FormData(form);
                formData.append('tab', this.theme.currentTab);
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(form.action || window.location.href, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message || 'Данните са запазени успешно.');

                        // Презареждане на страницата ако е необходимо
                        if (result.reload) {
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        }
                    } else {
                        self.showAlert('error', result.message || 'Възникна грешка при запазването.');
                    }
                })
                .catch(error => {
                    console.error('Form submission error:', error);
                    self.showAlert('error', 'Възникна грешка при изпращането на данните.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Показване на loading индикатор за темата
             */
            showThemeLoading: function() {
                if (!document.getElementById('theme-loading')) {
                    const loading = document.createElement('div');
                    loading.id = 'theme-loading';
                    loading.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
                    loading.innerHTML = `
                        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                            <span class="text-gray-700">Запазване...</span>
                        </div>
                    `;
                    document.body.appendChild(loading);
                }
            },

            /**
             * Скриване на loading индикатор за темата
             */
            hideThemeLoading: function() {
                const loading = document.getElementById('theme-loading');
                if (loading) {
                    loading.remove();
                }
            },

            /**
             * Получаване на текущия таб на темата
             */
            getCurrentThemeTab: function() {
                return this.theme.currentTab;
            },

            /**
             * Задаване на текущия таб на темата
             */
            setCurrentThemeTab: function(tabName) {
                this.theme.currentTab = tabName;
            }
        });
    }

})();
