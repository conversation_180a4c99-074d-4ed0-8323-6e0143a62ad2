/**
 * JavaScript функционалност за управление на слайдера в темата
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initThemeSlider();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за слайдера
            slider: {
                slideModal: null,
                currentSlideId: null,
                slides: [],
                slidesContainer: null,
                slideForm: null,
                sliderSettingsForm: null
            },

            /**
             * Инициализация на функционалността за слайдера
             */
            initThemeSlider: function() {
                this.initSliderElements();
                this.initSliderEvents();
                this.initSliderSortable();
            },

            /**
             * Инициализация на елементите за слайдера
             */
            initSliderElements: function() {
                this.slider.slideModal = document.getElementById('slide-modal');
                this.slider.slidesContainer = document.getElementById('slides-container');
                this.slider.slideForm = document.getElementById('slide-form');
                this.slider.sliderSettingsForm = document.getElementById('slider-settings-form');
            },

            /**
             * Свързване на събития за слайдера
             */
            initSliderEvents: function() {
                const self = this;

                // Бутон за добавяне на слайд
                const addSlideBtn = document.getElementById('add-slide-button');
                if (addSlideBtn) {
                    addSlideBtn.addEventListener('click', function() {
                        self.openSlideModal();
                    });
                }

                // Затваряне на модала
                document.querySelectorAll('.close-slide-modal').forEach(btn => {
                    btn.addEventListener('click', function() {
                        self.closeSlideModal();
                    });
                });

                // Клик извън модала
                if (this.slider.slideModal) {
                    this.slider.slideModal.addEventListener('click', function(e) {
                        if (e.target === self.slider.slideModal) {
                            self.closeSlideModal();
                        }
                    });
                }

                // Форма за слайд
                if (this.slider.slideForm) {
                    this.slider.slideForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveSlide();
                    });
                }

                // Форма за настройки на слайдера
                if (this.slider.sliderSettingsForm) {
                    this.slider.sliderSettingsForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveSliderSettings();
                    });
                }

                // Бутони в слайдовете
                this.initSlideButtons();

                // Добавяне на бутони в слайда
                const addButtonBtn = document.getElementById('add-slide-button-btn');
                if (addButtonBtn) {
                    addButtonBtn.addEventListener('click', function() {
                        self.addSlideButton();
                    });
                }

                // Избор на изображение
                this.initSliderImageSelection();

                // Slider за интервал
                this.initSliderInterval();
            },

            /**
             * Свързване на бутоните в слайдовете
             */
            initSlideButtons: function() {
                const self = this;

                // Редактиране на слайд
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.edit-slide')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        const slideId = slideItem.getAttribute('data-slide-id');
                        self.editSlide(slideId);
                    }
                });

                // Изтриване на слайд
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.delete-slide')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        const slideId = slideItem.getAttribute('data-slide-id');
                        self.deleteSlide(slideId);
                    }
                });

                // Преглед на слайд
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.preview-slide')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        const img = slideItem.querySelector('img');
                        self.previewSlide(img.src);
                    }
                });

                // Преместване нагоре/надолу
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.move-up')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        self.moveSlide(slideItem, 'up');
                    }
                    if (e.target.closest('.move-down')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        self.moveSlide(slideItem, 'down');
                    }
                });
            },

            /**
             * Свързване на избора на изображение за слайдера
             */
            initSliderImageSelection: function() {
                const self = this;

                document.addEventListener('click', function(e) {
                    if (e.target.closest('[data-action="select-slide-image"]')) {
                        e.preventDefault();
                        self.openSliderImageManager('slide');
                    }
                });
            },

            /**
             * Свързване на slider за интервал
             */
            initSliderInterval: function() {
                const slider = document.getElementById('slider-interval');
                const valueDisplay = document.getElementById('slider-interval-value');

                if (slider && valueDisplay) {
                    slider.addEventListener('input', function() {
                        valueDisplay.textContent = `${this.value} сек.`;
                    });
                }
            },

            /**
             * Отваряне на модала за слайд
             */
            openSlideModal: function(slideData) {
                slideData = slideData || null;
                this.slider.currentSlideId = slideData ? slideData.id : null;

                // Задаване на заглавието
                const title = document.getElementById('slide-modal-title');
                if (title) {
                    title.textContent = slideData ? 'Редактиране на слайд' : 'Добавяне на слайд';
                }

                // Попълване на формата
                if (slideData) {
                    this.populateSlideForm(slideData);
                } else {
                    this.resetSlideForm();
                }

                // Показване на модала
                if (this.slider.slideModal) {
                    this.slider.slideModal.classList.remove('hidden');
                }
            },

            /**
             * Затваряне на модала за слайд
             */
            closeSlideModal: function() {
                if (this.slider.slideModal) {
                    this.slider.slideModal.classList.add('hidden');
                }
                this.slider.currentSlideId = null;
                this.resetSlideForm();
            },

            /**
             * Попълване на формата за слайд
             */
            populateSlideForm: function(slideData) {
                const slideIdField = document.getElementById('slide-id');
                const slideTitleField = document.getElementById('slide-title');
                const slideSubtitleField = document.getElementById('slide-subtitle');
                const slideImageField = document.getElementById('slide-image-input');
                const slideStatusField = document.getElementById('slide-status');

                if (slideIdField) slideIdField.value = slideData.id || '';
                if (slideTitleField) slideTitleField.value = slideData.title || '';
                if (slideSubtitleField) slideSubtitleField.value = slideData.subtitle || '';
                if (slideImageField) slideImageField.value = slideData.image || '';
                if (slideStatusField) slideStatusField.checked = slideData.status || false;

                // Показване на изображението
                if (slideData.image_url) {
                    this.updateSlideImagePreview(slideData.image_url);
                }

                // Добавяне на бутоните
                const buttonsContainer = document.getElementById('slide-buttons-container');
                if (buttonsContainer) {
                    buttonsContainer.innerHTML = '';

                    if (slideData.buttons && slideData.buttons.length > 0) {
                        slideData.buttons.forEach(button => {
                            this.addSlideButton(button);
                        });
                    }
                }
            },

            /**
             * Нулиране на формата за слайд
             */
            resetSlideForm: function() {
                if (this.slider.slideForm) {
                    this.slider.slideForm.reset();
                }

                // Нулиране на изображението
                this.resetSlideImagePreview();

                // Изчистване на бутоните
                const buttonsContainer = document.getElementById('slide-buttons-container');
                if (buttonsContainer) {
                    buttonsContainer.innerHTML = '';
                }
            },

            /**
             * Обновяване на прегледа на изображението за слайд
             */
            updateSlideImagePreview: function(imageUrl) {
                const container = document.getElementById('slide-image-container');
                if (container) {
                    container.innerHTML = `
                        <div class="relative group" style="width: 192px; height: auto; min-height: 96px;">
                            <div class="aspect-auto rounded-lg overflow-hidden border border-gray-200">
                                <img src="${imageUrl}" alt="Slide image" class="w-full h-full object-contain">
                            </div>
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Промени изображение" data-action="select-slide-image">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-folder-image-line"></i>
                                    </div>
                                </button>
                                <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахни изображение" onclick="BackendModule.resetSlideImagePreview()">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>
                            </div>
                        </div>
                    `;
                }
            },

            /**
             * Нулиране на прегледа на изображението за слайд
             */
            resetSlideImagePreview: function() {
                const container = document.getElementById('slide-image-container');
                if (container) {
                    container.innerHTML = `
                        <div id="slide-image-placeholder" class="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" style="width: 192px; height: 96px;">
                            <div class="flex items-center space-x-2 mb-2">
                                <button type="button" data-action="select-slide-image" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                                    <i class="ri-folder-image-line ri-lg"></i>
                                </button>
                            </div>
                            <p class="text-xs text-gray-400 text-center">Няма изображение</p>
                        </div>
                    `;
                }

                // Нулиране на скритото поле
                const imageInput = document.getElementById('slide-image-input');
                if (imageInput) {
                    imageInput.value = '';
                }
            },

            /**
             * Добавяне на бутон в слайда
             */
            addSlideButton: function(buttonData) {
                buttonData = buttonData || null;
                const template = document.getElementById('slide-button-template');
                const container = document.getElementById('slide-buttons-container');

                if (template && container) {
                    const buttonElement = template.content.cloneNode(true);

                    if (buttonData) {
                        const textField = buttonElement.querySelector('.button-text');
                        const actionField = buttonElement.querySelector('.button-action');
                        const valueField = buttonElement.querySelector('.button-value');

                        if (textField) textField.value = buttonData.text || '';
                        if (actionField) actionField.value = buttonData.action || 'url';
                        if (valueField) valueField.value = buttonData.value || '';
                    }

                    // Добавяне на event listener за премахване
                    const removeBtn = buttonElement.querySelector('.remove-slide-button');
                    if (removeBtn) {
                        removeBtn.addEventListener('click', function(e) {
                            const buttonItem = e.target.closest('.slide-button-item');
                            if (buttonItem) {
                                buttonItem.remove();
                            }
                        });
                    }

                    container.appendChild(buttonElement);
                }
            },

            /**
             * Запазване на слайд
             */
            saveSlide: function() {
                const self = this;
                const formData = new FormData(this.slider.slideForm);

                // Добавяне на бутоните
                const buttons = [];
                document.querySelectorAll('.slide-button-item').forEach(item => {
                    const textField = item.querySelector('.button-text');
                    const actionField = item.querySelector('.button-action');
                    const valueField = item.querySelector('.button-value');

                    const text = textField ? textField.value : '';
                    const action = actionField ? actionField.value : '';
                    const value = valueField ? valueField.value : '';

                    if (text && value) {
                        buttons.push({ text, action, value });
                    }
                });

                formData.append('buttons', JSON.stringify(buttons));
                formData.append('action', this.slider.currentSlideId ? 'edit' : 'add');
                formData.append('tab', 'slider');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                        self.closeSlideModal();

                        // Презареждане на страницата за обновяване на списъка
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Save slide error:', error);
                    self.showAlert('error', 'Възникна грешка при запазването на слайда.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Запазване на настройките на слайдера
             */
            saveSliderSettings: function() {
                const self = this;
                const formData = new FormData(this.slider.sliderSettingsForm);
                formData.append('action', 'settings');
                formData.append('tab', 'slider');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Save settings error:', error);
                    self.showAlert('error', 'Възникна грешка при запазването на настройките.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Отваряне на image manager за слайдера
             */
            openSliderImageManager: function(type) {
                const self = this;
                const targetContainer = document.getElementById('slide-image-container');

                BackendModule.openImageManager({
                    singleSelection: true,
                    startDirectory: '',
                    target: targetContainer,
                    callback: function(selectedImages, target) {
                        if (selectedImages && selectedImages.length > 0 && target && type === 'slide') {
                            const selectedImage = selectedImages[0];
                            const imageInput = document.getElementById('slide-image-input');
                            const imgElement = target.querySelector('img');

                            if (imageInput) {
                                imageInput.value = selectedImage.path;
                            }

                            if (imgElement) {
                                imgElement.src = selectedImage.thumb;
                            }

                            self.updateSlideImagePreview(selectedImage.thumb);
                            self.showAlert('success', 'Изображението е успешно избрано.');
                        }
                    }
                });
            },

            /**
             * Инициализация на sortable за слайдовете
             */
            initSliderSortable: function() {
                // Тази функционалност ще бъде добавена когато е необходимо
                // за drag & drop пренареждане на слайдовете
            },

            /**
             * Редактиране на слайд
             */
            editSlide: function(slideId) {
                // Тази функционалност трябва да се имплементира според нуждите
                console.log('Edit slide:', slideId);
            },

            /**
             * Изтриване на слайд
             */
            deleteSlide: function(slideId) {
                // Тази функционалност трябва да се имплементира според нуждите
                console.log('Delete slide:', slideId);
            },

            /**
             * Преглед на слайд
             */
            previewSlide: function(imageSrc) {
                // Тази функционалност трябва да се имплементира според нуждите
                console.log('Preview slide:', imageSrc);
            },

            /**
             * Преместване на слайд
             */
            moveSlide: function(slideItem, direction) {
                // Тази функционалност трябва да се имплементира според нуждите
                console.log('Move slide:', direction);
            }
        });
    }

})();
