<?php

namespace Theme25\Backend\Controller\System\Theme;

/**
 * Суб-контролер за управление на страници
 *
 * @package Theme25\Backend\Controller\System\Theme
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Pages extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);

        $this->loadModels();
    }

    /**
     * Подготовка на данните за страниците
     */
    public function prepareData() {
        $this->preparePageItems()
             ->preparePageLinks()
             ->preparePagination();
        
        return $this;
    }

    /**
     * Зареждане на необходимите модели
     */
    private function loadModels() {
        $this->loadModelsAs([
            'catalog/information' => 'informationModel'
        ]);
        return $this;
    }

    /**
     * Подготовка на страниците
     */
    private function preparePageItems() {
        $filter_data = [
            'start' => ($this->getPage() - 1) * $this->getLimit(),
            'limit' => $this->getLimit()
        ];

        $pages = $this->informationModel->getInformations($filter_data);
        $total_pages = $this->informationModel->getTotalInformations();

        // Подготовка на данните за всяка страница
        $prepared_pages = [];
        foreach ($pages as $page) {
            $prepared_pages[] = [
                'information_id' => $page['information_id'],
                'title' => $page['title'],
                'status' => $page['status'],
                'date_modified' => date('d.m.Y H:i', strtotime($page['date_modified'])),
                'edit_url' => $this->getAdminLink('catalog/information/edit', 'information_id=' . $page['information_id']),
                'delete_url' => $this->getAdminLink('catalog/information/delete', 'information_id=' . $page['information_id'])
            ];
        }

        $this->setData([
            'page_items' => $prepared_pages,
            'total_pages' => $total_pages
        ]);

        return $this;
    }

    /**
     * Подготовка на линковете за страниците
     */
    private function preparePageLinks() {
        $links = [
            'add_page' => $this->getAdminLink('catalog/information/add'),
            'delete_selected' => $this->getAdminLink('catalog/information/delete')
        ];

        $this->setData('page_links', $links);

        return $this;
    }

    /**
     * Подготовка на пагинацията
     */
    private function preparePagination() {
        $total = $this->getData('total_pages') ?? 0;

        $pagination = new \Theme25\Pagination();
        $pagination->total = $total;
        $pagination->page = $this->getPage();
        $pagination->limit = $this->getLimit();
        $pagination->url = $this->getAdminLink('system/theme', 'tab=pages&page={page}');

        $this->setData('pagination', $pagination->render());

        return $this;
    }

    /**
     * Получаване на текущата страница
     */
    private function getPage() {
        return max(1, (int)$this->requestGet('page', 1));
    }

    /**
     * Получаване на лимита за страница
     */
    private function getLimit() {
        return 20; // Стандартен лимит за страници
    }

    /**
     * Запазване на страница (обработка на bulk операции)
     */
    public function save($data) {
        $json = ['success' => false, 'message' => ''];

        try {
            $action = $data['action'] ?? '';

            if ($action === 'bulk_status') {
                // Bulk обновяване на статуса
                $ids = json_decode($data['ids'] ?? '[]', true);
                $status = (int)($data['status'] ?? 0);

                if (empty($ids)) {
                    $json['message'] = 'Не са избрани страници.';
                } else {
                    foreach ($ids as $id) {
                        $this->informationModel->editInformation((int)$id, ['status' => $status]);
                    }

                    $status_text = $status ? 'активирани' : 'деактивирани';
                    $json = ['success' => true, 'message' => 'Страниците са ' . $status_text . ' успешно.'];
                }
            } else {
                // Пренасочване към information контролера за добавяне
                $json = ['success' => true, 'redirect' => $this->getAdminLink('catalog/information/add')];
            }

        } catch (Exception $e) {
            $json['message'] = 'Грешка при обработка: ' . $e->getMessage();
        }

        return $json;
    }

    /**
     * Изтриване на страница/страници
     */
    public function delete($id) {
        $json = ['success' => false, 'message' => ''];

        try {
            $action = $this->requestPost('action', '');

            if ($action === 'bulk_delete') {
                // Bulk изтриване
                $ids = json_decode($this->requestPost('ids', '[]'), true);

                if (empty($ids)) {
                    $json['message'] = 'Не са избрани страници за изтриване.';
                } else {
                    foreach ($ids as $page_id) {
                        $this->informationModel->deleteInformation((int)$page_id);
                    }

                    $count = count($ids);
                    $json = ['success' => true, 'message' => "Изтрити са {$count} страници успешно."];
                }
            } else {
                // Единично изтриване
                if ($id > 0) {
                    $this->informationModel->deleteInformation((int)$id);
                    $json = ['success' => true, 'message' => 'Страницата е изтрита успешно.'];
                } else {
                    $json['message'] = 'Невалиден ID на страница.';
                }
            }

        } catch (Exception $e) {
            $json['message'] = 'Грешка при изтриване: ' . $e->getMessage();
        }

        return $json;
    }
}
