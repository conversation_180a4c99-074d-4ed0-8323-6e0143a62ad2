[30-Jul-2025 06:13:33 UTC] PHP Fatal error:  Uncaught Error: Call to a member function addSliderItem() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme/Slider.php:153
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme/Slider.php(111): Theme25\Backend\Controller\System\Theme\Slider->saveSliderItem(Array)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme.php(118): Theme25\Backend\Controller\System\Theme\Slider->save(Array)
#2 [internal function]: Theme25\Backend\Controller\System\Theme->save()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('Theme25/Backend...', 'Save', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerSyste...', 'index', Array)
#6 /home/<USER>/storage_theme25/modification/system/engine/action.php(90): RequestProcessor->process('Controlle in /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme/Slider.php on line 153
[30-Jul-2025 06:15:29 UTC] PHP Fatal error:  Uncaught Error: Class 'Theme25\ThemeData' not found in /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme/Slider.php:201
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme/Slider.php(73): Theme25\Backend\Controller\System\Theme\Slider->getImageUrl('/slider/home-pa...')
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme/Slider.php(24): Theme25\Backend\Controller\System\Theme\Slider->prepareSliderItems()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme.php(80): Theme25\Backend\Controller\System\Theme\Slider->prepareData()
#3 [internal function]: Theme25\Backend\Controller\System\Theme->index()
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerSyste...', 'index', Array)
#6 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->pr in /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme/Slider.php on line 201
[30-Jul-2025 09:14:50 UTC] PHP Fatal error:  Uncaught Error: Call to a member function addBannerItem() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme/Banners.php:120
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme/Banners.php(81): Theme25\Backend\Controller\System\Theme\Banners->saveBannerItem(Array)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme.php(118): Theme25\Backend\Controller\System\Theme\Banners->save(Array)
#2 [internal function]: Theme25\Backend\Controller\System\Theme->save()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('Theme25/Backend...', 'Save', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerSyste...', 'index', Array)
#6 /home/<USER>/storage_theme25/modification/system/engine/action.php(90): RequestProcessor->process('Contro in /home/<USER>/storage_theme25/theme/Backend/Controller/System/Theme/Banners.php on line 120
