<?php

namespace Theme25\Model\System;

/**
 * Модел за работа с настройките на темата
 *
 * @package Theme25\Model\Catalog
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Information extends \Theme25\Model {

    /**
     * Получаване на настройките на слайдера
     */
    public function getInformation($information_id) {
		$query = $this->dbQuery("SELECT DISTINCT i.*, id.title 
        FROM " . DB_PREFIX . "information i 
        LEFT JOIN " . DB_PREFIX . "information_description id ON (i.information_id = id.information_id)
        WHERE information_id = '" . (int)$information_id . "'");

		return $query->row;
	}
   
}
