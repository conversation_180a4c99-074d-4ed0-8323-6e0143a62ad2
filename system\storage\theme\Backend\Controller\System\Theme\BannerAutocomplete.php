<?php

namespace Theme25\Backend\Controller\System\Theme;

/**
 * Суб-контролер за автодопълване на банери
 *
 * @package Theme25\Backend\Controller\System\Theme
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class BannerAutocomplete extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->loadThemeModel();
    }

    /**
     * Зареждане на модела за темата
     */
    private function loadThemeModel() {
        $this->loadModelAs('system/theme', 'themeModel');
    }

    /**
     * Автодопълване за банери
     */
    public function autocomplete($params) {
        $json = [];

        if (isset($params['id']) && $params['id'] > 0) {
            // Получаване на данни за конкретен банер
            $banner = $this->themeModel->getBannerItem($params['id']);
            
            if ($banner) {
                $json = [
                    'id' => $banner['id'],
                    'title' => $banner['title'],
                    'image' => $banner['image'],
                    'image_url' => $this->getImageUrl($banner['image']),
                    'link' => $banner['link'],
                    'page_id' => $banner['page_id'],
                    'status' => (bool)$banner['status']
                ];
            }
        }

        return $json;
    }

    /**
     * Генериране на URL за изображение
     */
    private function getImageUrl($image) {
        if (empty($image)) {
            return '';
        }
        
        // Използваме \Theme25\Data за получаване на URL-а на изображението
        return \Theme25\Data::getInstance()->getImageWebUrl() . $image;
    }
}
