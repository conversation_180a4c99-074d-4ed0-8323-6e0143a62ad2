/**
 * JavaScript функционалност за настройки на темата
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initTheme();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за темата
            theme: {
                currentTab: 'slider',
                modules: {},
                tabCache: new Map(),
                tabFormData: new Map(),
                config: {
                    userToken: new URLSearchParams(window.location.search).get('user_token') || '',
                    baseUrl: '',
                    ajaxUrls: {}
                }
            },

            /**
             * Инициализация на функционалността за темата
             */
            initTheme: function() {
                this.loadThemeConfig();
                this.initThemeTabSwitching();
                this.initThemeEvents();
            },

            /**
             * Зареждане на конфигурация за темата
             */
            loadThemeConfig: function() {
                // Зареждане на конфигурация от глобални променливи
                if (window.themeConfig) {
                    this.theme.config = { ...this.theme.config, ...window.themeConfig };
                }

                // Извличане на user_token от URL
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('user_token')) {
                    this.theme.config.userToken = urlParams.get('user_token');
                }

                // Определяне на текущия таб
                this.theme.currentTab = urlParams.get('tab') || 'slider';
            },

            /**
             * Инициализация на превключването между табове
             */
            initThemeTabSwitching: function() {
                const tabButtons = document.querySelectorAll('.tab-button');
                const tabContents = document.querySelectorAll('.tab-content');

                tabButtons.forEach((button) => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();

                        const tabName = button.getAttribute('data-tab');
                        if (tabName) {
                            this.switchThemeTab(tabName);
                        }
                    });
                });
            },

            /**
             * Превключване на таб със статично съдържание
             */
            switchThemeTab: function(tabName) {
                if (tabName === this.theme.currentTab) {
                    return; // Вече е активен
                }

                // Обновяване на активните бутони
                this.updateActiveTabButton(tabName);

                // Скриване на всички табове
                document.querySelectorAll('.tab-content').forEach(tabContent => {
                    tabContent.classList.add('hidden');
                });

                // Показване на избрания таб
                const targetContent = document.getElementById('content-' + tabName);
                if (targetContent) {
                    targetContent.classList.remove('hidden');

                    // Ако таба не е зареждан преди това, зареди го
                    if (!this.theme.tabCache.has(tabName)) {
                        this.loadTabContentIfNeeded(tabName, targetContent);
                    }
                }

                // Обновяване на URL
                const url = new URL(window.location);
                url.searchParams.set('tab', tabName);
                window.history.pushState({}, '', url);

                // Запазване на текущия таб
                this.theme.currentTab = tabName;

                // Инициализация на специфичните функции за таба
                this.initTabSpecificFunctions(tabName);
            },

            /**
             * Обновяване на активния бутон за таб
             */
            updateActiveTabButton: function(tabName) {
                const tabButtons = document.querySelectorAll('.tab-button');

                tabButtons.forEach(button => {
                    const buttonTab = button.getAttribute('data-tab');

                    if (buttonTab === tabName) {
                        button.classList.add('active', 'text-primary');
                        button.classList.remove('text-gray-600');
                    } else {
                        button.classList.remove('active', 'text-primary');
                        button.classList.add('text-gray-600');
                    }
                });
            },

            /**
             * Зареждане на съдържанието на таб ако е необходимо
             */
            loadTabContentIfNeeded: function(tabName, targetElement) {
                const self = this;

                // Проверка дали съдържанието е кеширано
                if (self.theme.tabCache.has(tabName)) {
                    return; // Вече е заредено
                }

                // Проверка дали таба има placeholder съдържание
                const hasPlaceholder = targetElement.querySelector('.text-center.py-8');
                if (!hasPlaceholder) {
                    // Таба вече има реално съдържание, маркираме го като заредено
                    self.theme.tabCache.set(tabName, 'loaded');
                    return;
                }

                // Показване на loading индикатор
                this.showTabLoading(tabName);

                // AJAX заявка за зареждане на съдържанието
                const url = self.theme.config.baseUrl + '&tab=' + tabName + '&user_token=' + self.theme.config.userToken;

                fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(html => {
                    // Кеширане на съдържанието
                    self.theme.tabCache.set(tabName, html);

                    // Показване на съдържанието
                    targetElement.innerHTML = html;

                    // Инициализация на специфичните функции за таба
                    self.initTabSpecificFunctions(tabName);
                })
                .catch(error => {
                    console.error('Error loading tab content:', error);
                    self.showAlert('error', 'Грешка при зареждане на съдържанието на таба.');
                });
            },

            /**
             * Показване на loading индикатор за таб
             */
            showTabLoading: function(tabName) {
                const targetContent = document.getElementById('content-' + tabName);
                if (targetContent) {
                    targetContent.innerHTML = `
                        <div class="bg-white rounded shadow p-6">
                            <div class="text-center py-8">
                                <div class="animate-spin inline-block w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mb-4"></div>
                                <p class="text-gray-500">Зареждане...</p>
                            </div>
                        </div>
                    `;
                    targetContent.classList.remove('hidden');
                }
            },

            /**
             * Инициализация на специфични функции за таб
             */
            initTabSpecificFunctions: function(tabName) {
                // Инициализация на модула за конкретния таб
                switch (tabName) {
                    case 'slider':
                        if (this.theme.modules.slider) {
                            this.theme.modules.slider.init();
                        }
                        break;
                    case 'banners':
                        if (this.theme.modules.banners) {
                            this.theme.modules.banners.init();
                        }
                        break;
                    case 'pages':
                        if (this.theme.modules.pages) {
                            this.theme.modules.pages.init();
                        }
                        break;
                }
            },

            /**
             * Свързване на общи събития за темата
             */
            initThemeEvents: function() {
                // Обработка на промени в URL-а
                window.addEventListener('popstate', () => {
                    const urlParams = new URLSearchParams(window.location.search);
                    const tab = urlParams.get('tab') || 'slider';
                    this.switchThemeTab(tab);
                });

                // Обработка на форми за запазване
                this.initThemeSaveForms();
            },

            /**
             * Свързване на форми за запазване
             */
            initThemeSaveForms: function() {
                // Общ обработчик за AJAX форми
                document.addEventListener('submit', (e) => {
                    const form = e.target;
                    if (form.classList.contains('ajax-form') || form.hasAttribute('data-ajax')) {
                        e.preventDefault();
                        this.submitThemeForm(form);
                    }
                });
            },

            /**
             * Изпращане на форма чрез AJAX
             */
            submitThemeForm: function(form) {
                const self = this;
                const formData = new FormData(form);
                formData.append('tab', this.theme.currentTab);
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(form.action || window.location.href, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message || 'Данните са запазени успешно.');

                        // Презареждане на страницата ако е необходимо
                        if (result.reload) {
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        }
                    } else {
                        self.showAlert('error', result.message || 'Възникна грешка при запазването.');
                    }
                })
                .catch(error => {
                    console.error('Form submission error:', error);
                    self.showAlert('error', 'Възникна грешка при изпращането на данните.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Показване на loading индикатор за темата
             */
            showThemeLoading: function() {
                if (!document.getElementById('theme-loading')) {
                    const loading = document.createElement('div');
                    loading.id = 'theme-loading';
                    loading.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
                    loading.innerHTML = `
                        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                            <span class="text-gray-700">Запазване...</span>
                        </div>
                    `;
                    document.body.appendChild(loading);
                }
            },

            /**
             * Скриване на loading индикатор за темата
             */
            hideThemeLoading: function() {
                const loading = document.getElementById('theme-loading');
                if (loading) {
                    loading.remove();
                }
            },

            /**
             * Получаване на текущия таб на темата
             */
            getCurrentThemeTab: function() {
                return this.theme.currentTab;
            },

            /**
             * Задаване на текущия таб на темата
             */
            setCurrentThemeTab: function(tabName) {
                this.theme.currentTab = tabName;
            }
        });
    }

})();
