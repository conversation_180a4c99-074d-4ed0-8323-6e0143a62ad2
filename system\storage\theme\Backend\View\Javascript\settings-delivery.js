/**
 * JavaScript модул за настройки на доставка
 * Следва BackendModule pattern на темата Rakla.bg
 * Разширява основния settings модул
 */
(function() {
    'use strict';

    // Функция за инициализация на delivery модула
    function initDeliveryModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModuleWithDelivery();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул с функционалности за доставка
    function extendBackendModuleWithDelivery() {
        Object.assign(BackendModule, {
            /**
             * Инициализиране на настройки за доставка
             */
            initDeliverySettings: function() {
                this.bindDeliverySettingsEvents();
                this.initPricingTypeHandlers();
                this.initModalHandlers();
                this.logDev && this.logDev('Delivery settings module initialized');
            },

            /**
             * Свързване на събития за настройки за доставка
             */
            bindDeliverySettingsEvents: function() {
                const self = this;

                // Бутон за запазване
                const saveButton = document.getElementById('save-delivery-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveDeliverySettings();
                    });
                }

                // Toggle за методи на доставка
                const deliveryToggles = document.querySelectorAll('.delivery-method-toggle');
                deliveryToggles.forEach(toggle => {
                    toggle.addEventListener('change', function() {
                        self.toggleDeliveryMethod(this.dataset.method, this.checked);
                    });
                });

                // Тест на методи на доставка
                const testButtons = document.querySelectorAll('.test-delivery-method');
                testButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testDeliveryConnection(this.dataset.method);
                    });
                });

                // Конфигуриране на методи на доставка
                const configButtons = document.querySelectorAll('.config-delivery-method');
                configButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.showConfigModal(this.dataset.method);
                    });
                });
            },

            /**
             * Инициализиране на обработчици за типове цени
             */
            initPricingTypeHandlers: function() {
                const pricingSelects = document.querySelectorAll('select[name*="[pricing_type]"]');
                pricingSelects.forEach(select => {
                    select.addEventListener('change', function() {
                        const fixedPriceContainer = this.closest('.flex').nextElementSibling.querySelector('.flex-1');
                        if (fixedPriceContainer) {
                            fixedPriceContainer.style.display = this.value === 'fixed' ? 'block' : 'none';
                        }
                    });
                });
            },

            /**
             * Инициализиране на обработчици за модали
             */
            initModalHandlers: function() {
                const self = this;

                // Затваряне на модали
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('close-modal') || e.target.closest('.close-modal')) {
                        self.closeModal();
                    }
                });

                // Запазване на конфигурация
                const saveConfigButton = document.getElementById('save-courier-config');
                if (saveConfigButton) {
                    saveConfigButton.addEventListener('click', function() {
                        self.saveCourierConfig();
                    });
                }
            },

     
            /**
             * Обработка на click събития за delivery таб
             */
            handleDeliveryClick: function(e) {
                const target = e.target;
                const self = this;

                const targetAction = e.target.closest('[data-action]');
                if (targetAction) {

                    const action = targetAction.getAttribute('data-action');
                    const courierCode = targetAction.getAttribute('data-courier');

                    console.log(action);
                    console.log(courierCode);

                    switch (action) {
                        case 'select-courier-logo':
                            e.preventDefault();
                            self.selectCourierLogo(courierCode);
                            break;
                        case 'remove-courier-logo':
                            e.preventDefault();
                            self.removeCourierLogo(courierCode);
                            break;
                    }
                }


                if (target.id === 'save-delivery-settings') {
                    e.preventDefault();
                    self.saveDeliverySettings();
                } else if (target.classList.contains('test-delivery-method')) {
                    e.preventDefault();
                    self.testDeliveryMethod(target.dataset.method);
                } else if (target.classList.contains('config-delivery-method')) {
                    e.preventDefault();
                    self.configureDeliveryMethod(target.dataset.method);
                } else if (target.id === 'add-delivery-zone') {
                    e.preventDefault();
                    self.showAddZoneModal();
                } else if (target.classList.contains('edit-zone')) {
                    e.preventDefault();
                    self.editZone(target.dataset.zoneId);
                } else if (target.classList.contains('delete-zone')) {
                    e.preventDefault();
                    self.deleteZone(target.dataset.zoneId);
                }
            },

            /**
             * Обработка на form submit събития за delivery таб
             */
            handleDeliveryFormSubmit: function(e) {
                e.preventDefault();
                const form = e.target;
                const self = this;

                if (form.id === 'delivery-settings-form') {
                    self.saveDeliverySettings();
                } else if (form.id === 'delivery-method-config-form') {
                    self.saveDeliveryMethodConfig(form);
                } else if (form.id === 'add-zone-form') {
                    self.submitAddZoneForm(form);
                }
            },

            /**
             * Обработка на change събития за delivery таб
             */
            handleDeliveryChange: function(e) {
                const target = e.target;
                const self = this;

                if (target.classList.contains('delivery-method-toggle')) {
                    self.toggleDeliveryMethod(target.dataset.method, target.checked);
                }

                // Автоматично запазване при промяна на checkbox-и
                if (target.type === 'checkbox' && target.name && target.name.includes('setting')) {
                    self.autoSaveSettings();
                }
            },

            /**
             * Запазване на настройки за доставка
             */
            saveDeliverySettings: function() {
                const self = this;

                // Събиране на данни от всички форми
                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                // Събиране на данни за всеки метод за доставка
                const deliveryMethods = {};
                document.querySelectorAll('[data-method]').forEach(methodCard => {
                    const methodCode = methodCard.dataset.method;
                    const methodData = {};

                    // Статус
                    const statusToggle = methodCard.querySelector('.delivery-method-toggle');
                    if (statusToggle) {
                        methodData.status = statusToggle.checked ? 1 : 0;
                    }

                    // Потребителско име и парола
                    const usernameInput = methodCard.querySelector('input[name*="[username]"]');
                    if (usernameInput) {
                        methodData.username = usernameInput.value;
                    }

                    const passwordInput = methodCard.querySelector('input[name*="[password]"]');
                    if (passwordInput) {
                        methodData.password = passwordInput.value;
                    }

                    // Тип цена
                    const pricingTypeSelect = methodCard.querySelector('select[name*="[pricing_type]"]');
                    if (pricingTypeSelect) {
                        methodData.pricing_type = pricingTypeSelect.value;
                    }

                    // Фиксирана цена
                    const fixedPriceInput = methodCard.querySelector('input[name*="[fixed_price]"]');
                    if (fixedPriceInput) {
                        methodData.fixed_price = fixedPriceInput.value;
                    }

                    // Лого
                    const logoInput = methodCard.querySelector('input[name*="[logo_path]"]');
                    if (logoInput) {
                        methodData.logo_path = logoInput.value;
                    }

                    deliveryMethods[methodCode] = methodData;
                });

                formData.append('delivery_methods', JSON.stringify(deliveryMethods));

                // Показване на loading състояние
                const saveButton = document.getElementById('save-delivery-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    } else if (data.errors) {
                        data.errors.forEach(error => {
                            self.showSettingsNotification(error, 'error');
                        });
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving delivery settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази промените';
                    }
                });
            },

            /**
             * Тестване на връзката с куриерския сървър
             */
            testDeliveryConnection: function(methodCode) {
                const self = this;
                const testButton = document.querySelector(`[data-method="${methodCode}"].test-delivery-method`);

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('code', methodCode);

                fetch(self.settings.config.ajaxUrls.testConnection || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'Връзката е успешна', 'success');
                    } else {
                        self.showSettingsNotification(data.message || 'Грешка при тестването', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing delivery connection:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-wifi-line mr-2"></i>Тест на връзката';
                    }
                });
            },

            /**
             * Toggle на метод на доставка
             */
            toggleDeliveryMethod: function(methodCode, enabled) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('code', methodCode);
                formData.append('status', enabled ? '1' : '0');

                fetch(self.settings.config.ajaxUrls.toggleMethod || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на визуалното състояние
                        const methodCard = document.querySelector(`[data-method="${methodCode}"]`);
                        if (methodCard) {
                            if (enabled) {
                                methodCard.classList.remove('opacity-50');
                                methodCard.classList.add('border-green-200', 'bg-green-50');
                            } else {
                                methodCard.classList.add('opacity-50');
                                methodCard.classList.remove('border-green-200', 'bg-green-50');
                            }
                        }
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при актуализирането', 'error');

                        // Връщане на toggle-а в предишното състояние
                        const toggle = document.querySelector(`[data-method="${methodCode}"].delivery-method-toggle`);
                        if (toggle) {
                            toggle.checked = !enabled;
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error toggling delivery method:', error);
                    self.showSettingsNotification('Възникна грешка при промяната', 'error');
                });
            },

            /**
             * Показване на модал за конфигуриране
             */
            showConfigModal: function(methodCode) {
                const self = this;
                const modal = document.getElementById('courier-config-modal');
                const title = document.getElementById('modal-courier-title');
                const container = document.getElementById('config-fields-container');

                if (!modal || !title || !container) {
                    self.showSettingsNotification('Модалът не е намерен', 'error');
                    return;
                }

                // Получаване на данните за метода
                const methodData = self.settings.config.deliveryMethods[methodCode];
                if (!methodData) {
                    self.showSettingsNotification('Методът не е намерен', 'error');
                    return;
                }

                // Задаване на заглавие
                title.textContent = `Конфигуриране на ${methodData.name}`;

                // Генериране на полетата
                container.innerHTML = '';
                const configFields = methodData.config_fields || {};

                Object.keys(configFields).forEach(fieldName => {
                    const fieldConfig = configFields[fieldName];
                    const fieldValue = (methodData.settings && methodData.settings[fieldName]) || fieldConfig.default || '';

                    const fieldDiv = document.createElement('div');
                    fieldDiv.className = 'mb-4';

                    let fieldHtml = `<label class="block text-sm font-medium text-gray-700 mb-1">${fieldConfig.label}`;
                    if (fieldConfig.required) {
                        fieldHtml += ' <span class="text-red-500">*</span>';
                    }
                    fieldHtml += '</label>';

                    switch (fieldConfig.type) {
                        case 'text':
                            fieldHtml += `<input type="text" name="${fieldName}" value="${fieldValue}"
                                         class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                         ${fieldConfig.placeholder ? `placeholder="${fieldConfig.placeholder}"` : ''}
                                         ${fieldConfig.required ? 'required' : ''}>`;
                            break;
                        case 'password':
                            fieldHtml += `<input type="password" name="${fieldName}" value="${fieldValue}"
                                         class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                         ${fieldConfig.required ? 'required' : ''}>`;
                            break;
                        case 'textarea':
                            fieldHtml += `<textarea name="${fieldName}" rows="3"
                                         class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                         ${fieldConfig.placeholder ? `placeholder="${fieldConfig.placeholder}"` : ''}
                                         ${fieldConfig.required ? 'required' : ''}>${fieldValue}</textarea>`;
                            break;
                        case 'checkbox':
                            fieldHtml += `<label class="flex items-center">
                                         <input type="checkbox" name="${fieldName}" value="1"
                                                class="mr-2" ${fieldValue ? 'checked' : ''}>
                                         <span class="text-sm text-gray-700">${fieldConfig.label}</span>
                                         </label>`;
                            break;
                    }

                    fieldDiv.innerHTML = fieldHtml;
                    container.appendChild(fieldDiv);
                });

                // Запазване на кода на метода за по-късно използване
                modal.dataset.methodCode = methodCode;

                // Показване на модала
                modal.classList.remove('hidden');
            },

            /**
             * Показване на modal за добавяне на зона
             */
            showAddZoneModal: function() {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на зона за доставка</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-zone-form" class="p-4">
                            <div class="space-y-4">
                                <div>
                                    <label for="zone_name" class="block text-sm font-medium text-gray-700 mb-1">Име на зоната</label>
                                    <input type="text" id="zone_name" name="name" required 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="zone_description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
                                    <textarea id="zone_description" name="description" rows="3" 
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"></textarea>
                                </div>
                                <div>
                                    <label for="zone_price" class="block text-sm font-medium text-gray-700 mb-1">Цена за доставка</label>
                                    <input type="number" id="zone_price" name="price" step="0.01" min="0" required 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="zone_status" name="status" value="1" checked 
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                    <label for="zone_status" class="ml-2 block text-sm text-gray-900">Активна</label>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                    Отказ
                                </button>
                                <button type="submit" id="save-new-zone" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                    <i class="ri-save-line mr-2"></i>Запази
                                </button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(modal);
            },

            /**
             * Инициализиране на sortable за shipping методи
             */
            initShippingMethodsSortable: function() {
                const shippingMethodsList = document.getElementById('shipping-methods-list');
                if (shippingMethodsList && typeof Sortable !== 'undefined') {
                    new Sortable(shippingMethodsList, {
                        animation: 150,
                        ghostClass: 'sortable-ghost',
                        onEnd: () => {
                            this.reorderShippingMethods();
                        }
                    });
                }
            },

            /**
             * Преподреждане на shipping методи
             */
            reorderShippingMethods: function() {
                const self = this;
                const shippingMethodsList = document.getElementById('shipping-methods-list');

                if (!shippingMethodsList) return;

                const methodCodes = [];
                const methodRows = shippingMethodsList.querySelectorAll('[data-method]');

                methodRows.forEach((row, index) => {
                    const methodCode = row.dataset.method;
                    if (methodCode) {
                        methodCodes.push({
                            code: methodCode,
                            sort_order: index + 1
                        });
                    }
                });

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_order', JSON.stringify(methodCodes));

                fetch(self.settings.config.ajaxUrls.reorder_shipping_methods || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification('Подредбата е запазена', 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error reordering shipping methods:', error);
                    self.showSettingsNotification('Възникна грешка при преподреждането', 'error');
                });
            },

            /**
             * Затваряне на модала
             */
            closeModal: function() {
                const modal = document.getElementById('courier-config-modal');
                if (modal) {
                    modal.classList.add('hidden');
                    modal.dataset.methodCode = '';

                    // Изчистване на формата
                    const form = document.getElementById('courier-config-form');
                    if (form) {
                        form.reset();
                    }
                }
            },

            /**
             * Запазване на конфигурацията на куриера
             */
            saveCourierConfig: function() {
                const self = this;
                const modal = document.getElementById('courier-config-modal');
                const form = document.getElementById('courier-config-form');

                if (!modal || !form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                const methodCode = modal.dataset.methodCode;
                if (!methodCode) {
                    self.showSettingsNotification('Методът не е определен', 'error');
                    return;
                }

                // Събиране на данните от формата
                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);
                formData.append('code', methodCode);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-courier-config');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.saveMethodConfig || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                        self.closeModal();
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при запазването', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving courier config:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Избор на лого за куриер
             */
            selectCourierLogo: function(courierCode) {
                const self = this;

                if (typeof BackendModule === 'undefined' || !BackendModule.openImageManager) {
                    self.showSettingsNotification('ImageManager не е наличен', 'error');
                    return;
                }

                BackendModule.openImageManager({
                    singleSelection: true,
                    callback: function(images) {
                        if (images && images.length > 0) {
                            self.setCourierLogo(courierCode, images[0]);
                        }
                    },
                    target: document.getElementById(`input-courier-logo-${courierCode}`)
                });
            },

            /**
             * Задаване на лого за куриер
             */
            setCourierLogo: function(courierCode, image) {
                const container = document.getElementById(`courier-logo-container-${courierCode}`);
                const input = document.getElementById(`input-courier-logo-${courierCode}`);

                if (!container || !input) {
                    this.showSettingsNotification('Контейнерът за лого не е намерен', 'error');
                    return;
                }

                // Актуализиране на скритото поле
                input.value = image.path;

                // Създаване на новия HTML за логото
                const logoHtml = `
                    <div class="relative group w-full h-full">
                        <div class="w-full h-full rounded-lg overflow-hidden border border-gray-200">
                            <img src="${image.thumb}" alt="Лого на куриера" id="courier-logo-preview-${courierCode}" class="w-full h-full object-contain">
                        </div>
                        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                            <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Промени лого" data-action="select-courier-logo" data-courier="${courierCode}">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-folder-image-line"></i>
                                </div>
                            </button>
                            <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахни лого" data-action="remove-courier-logo" data-courier="${courierCode}">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-delete-bin-line"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                `;

                container.innerHTML = logoHtml;
                this.showSettingsNotification('Логото е избрано успешно', 'success');
            },

            /**
             * Премахване на лого за куриер
             */
            removeCourierLogo: function(courierCode) {
                const container = document.getElementById(`courier-logo-container-${courierCode}`);
                const input = document.getElementById(`input-courier-logo-${courierCode}`);

                if (!container || !input) {
                    this.showSettingsNotification('Контейнерът за лого не е намерен', 'error');
                    return;
                }

                // Изчистване на скритото поле
                input.value = '';

                // Създаване на placeholder HTML
                const placeholderHtml = `
                    <div class="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors w-full h-full">
                        <div class="flex items-center space-x-2 mb-2">
                            <button type="button" data-action="select-courier-logo" data-courier="${courierCode}" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                                <i class="ri-folder-image-line ri-lg"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-400 text-center">Няма лого</p>
                    </div>
                `;

                container.innerHTML = placeholderHtml;
                this.showSettingsNotification('Логото е премахнато успешно', 'success');
            }
        });
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initDeliveryModule();
    });

})();
