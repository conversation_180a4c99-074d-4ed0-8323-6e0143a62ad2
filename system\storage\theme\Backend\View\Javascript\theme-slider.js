/**
 * JavaScript функционалност за управление на слайдера в темата
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initThemeSlider();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за слайдера
            slider: {
                slideModal: null,
                currentSlideId: null,
                slides: [],
                slidesContainer: null,
                slideForm: null,
                sliderSettingsForm: null
            },

            /**
             * Инициализация на функционалността за слайдера
             */
            initThemeSlider: function() {
                this.initSliderElements();
                this.initSliderEvents();
                this.initSliderSortable();
            },

            /**
             * Инициализация на елементите за слайдера
             */
            initSliderElements: function() {
                this.slider.slideModal = document.getElementById('slide-modal');
                this.slider.slidesContainer = document.getElementById('slides-container');
                this.slider.slideForm = document.getElementById('slide-form');
                this.slider.sliderSettingsForm = document.getElementById('slider-settings-form');
            },

            /**
             * Свързване на събития за слайдера
             */
            initSliderEvents: function() {
                const self = this;

                // Бутон за добавяне на слайд
                const addSlideBtn = document.getElementById('add-slide-button');
                if (addSlideBtn) {
                    addSlideBtn.addEventListener('click', function() {
                        self.openSlideModal();
                    });
                }

                // Затваряне на модала
                document.querySelectorAll('.close-slide-modal').forEach(btn => {
                    btn.addEventListener('click', function() {
                        self.closeSlideModal();
                    });
                });

                // Клик извън модала
                if (this.slider.slideModal) {
                    this.slider.slideModal.addEventListener('click', function(e) {
                        if (e.target === self.slider.slideModal) {
                            self.closeSlideModal();
                        }
                    });
                }

                // Форма за слайд
                if (this.slider.slideForm) {
                    this.slider.slideForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveSlide();
                    });
                }

                // Форма за настройки на слайдера
                if (this.slider.sliderSettingsForm) {
                    this.slider.sliderSettingsForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveSliderSettings();
                    });
                }

                // Бутони в слайдовете
                this.initSlideButtons();

                // Добавяне на бутони в слайда
                const addButtonBtn = document.getElementById('add-slide-button-btn');
                if (addButtonBtn) {
                    addButtonBtn.addEventListener('click', function() {
                        self.addSlideButton();
                    });
                }

                // Избор на изображение
                this.initSliderImageSelection();

                // Slider за интервал
                this.initSliderInterval();
            },

            /**
             * Свързване на бутоните в слайдовете
             */
            initSlideButtons: function() {
                const self = this;

                // Редактиране на слайд
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.edit-slide')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        const slideId = slideItem.getAttribute('data-slide-id');
                        self.editSlide(slideId);
                    }
                });

                // Изтриване на слайд
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.delete-slide')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        const slideId = slideItem.getAttribute('data-slide-id');
                        self.deleteSlide(slideId);
                    }
                });

                // Преглед на слайд
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.preview-slide')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        const img = slideItem.querySelector('img');
                        self.previewSlide(img.src);
                    }
                });

                // Преместване нагоре/надолу
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.move-up')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        self.moveSlide(slideItem, 'up');
                    }
                    if (e.target.closest('.move-down')) {
                        e.preventDefault();
                        const slideItem = e.target.closest('.slide-item');
                        self.moveSlide(slideItem, 'down');
                    }
                });
            },

            /**
             * Свързване на избора на изображение за слайдера
             */
            initSliderImageSelection: function() {
                const self = this;

                document.addEventListener('click', function(e) {
                    if (e.target.closest('[data-action="select-slide-image"]')) {
                        e.preventDefault();
                        self.openSliderImageManager('slide');
                    }
                });
            },

            /**
             * Свързване на slider за интервал
             */
            initSliderInterval: function() {
                const slider = document.getElementById('slider-interval');
                const valueDisplay = document.getElementById('slider-interval-value');

                if (slider && valueDisplay) {
                    slider.addEventListener('input', function() {
                        valueDisplay.textContent = `${this.value} сек.`;
                    });
                }
            },

            /**
             * Отваряне на модала за слайд
             */
            openSlideModal: function(slideData) {
                slideData = slideData || null;
                this.slider.currentSlideId = slideData ? slideData.id : null;

                // Задаване на заглавието
                const title = document.getElementById('slide-modal-title');
                if (title) {
                    title.textContent = slideData ? 'Редактиране на слайд' : 'Добавяне на слайд';
                }

                // Попълване на формата
                if (slideData) {
                    this.populateSlideForm(slideData);
                } else {
                    this.resetSlideForm();
                }

                // Показване на модала
                if (this.slider.slideModal) {
                    this.slider.slideModal.classList.remove('hidden');
                }
            },

            /**
             * Затваряне на модала за слайд
             */
            closeSlideModal: function() {
                if (this.slider.slideModal) {
                    this.slider.slideModal.classList.add('hidden');
                }
                this.slider.currentSlideId = null;
                this.resetSlideForm();
            },

            /**
             * Попълване на формата за слайд
             */
            populateSlideForm: function(slideData) {
                const slideIdField = document.getElementById('slide-id');
                const slideTitleField = document.getElementById('slide-title');
                const slideSubtitleField = document.getElementById('slide-subtitle');
                const slideImageField = document.getElementById('slide-image-input');
                const slideStatusField = document.getElementById('slide-status');

                if (slideIdField) slideIdField.value = slideData.id || '';
                if (slideTitleField) slideTitleField.value = slideData.title || '';
                if (slideSubtitleField) slideSubtitleField.value = slideData.subtitle || '';
                if (slideImageField) slideImageField.value = slideData.image || '';
                if (slideStatusField) slideStatusField.checked = slideData.status || false;

                // Показване на изображението
                if (slideData.image_url) {
                    this.updateSlideImagePreview(slideData.image_url);
                }

                // Добавяне на бутоните
                const buttonsContainer = document.getElementById('slide-buttons-container');
                if (buttonsContainer) {
                    buttonsContainer.innerHTML = '';

                    if (slideData.buttons && slideData.buttons.length > 0) {
                        slideData.buttons.forEach(button => {
                            this.addSlideButton(button);
                        });
                    }
                }
            },

            /**
             * Нулиране на формата за слайд
             */
            resetSlideForm: function() {
                if (this.slider.slideForm) {
                    this.slider.slideForm.reset();
                }

                // Нулиране на изображението
                this.resetSlideImagePreview();

                // Изчистване на бутоните
                const buttonsContainer = document.getElementById('slide-buttons-container');
                if (buttonsContainer) {
                    buttonsContainer.innerHTML = '';
                }
            },

            /**
             * Обновяване на прегледа на изображението за слайд
             */
            updateSlideImagePreview: function(imageUrl) {
                const container = document.getElementById('slide-image-container');
                if (container) {
                    container.innerHTML = `
                        <div class="relative group slide-image-preview">
                            <div class="aspect-auto rounded-lg overflow-hidden border border-gray-200">
                                <img src="${imageUrl}" alt="Slide image" class="w-full h-auto object-cover">
                            </div>
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Промени изображение" data-action="select-slide-image">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-folder-image-line"></i>
                                    </div>
                                </button>
                                <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахни изображение" onclick="BackendModule.resetSlideImagePreview()">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>
                            </div>
                        </div>
                    `;
                }
            },

            /**
             * Нулиране на прегледа на изображението за слайд
             */
            resetSlideImagePreview: function() {
                const container = document.getElementById('slide-image-container');
                if (container) {
                    container.innerHTML = `
                        <div id="slide-image-placeholder" class="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" style="width: 192px; height: 96px;">
                            <div class="flex items-center space-x-2 mb-2">
                                <button type="button" data-action="select-slide-image" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                                    <i class="ri-folder-image-line ri-lg"></i>
                                </button>
                            </div>
                            <p class="text-xs text-gray-400 text-center">Няма изображение</p>
                        </div>
                    `;
                }

                // Нулиране на скритото поле
                const imageInput = document.getElementById('slide-image-input');
                if (imageInput) {
                    imageInput.value = '';
                }
            },

            /**
             * Добавяне на бутон в слайда
             */
            addSlideButton: function(buttonData) {
                const self = this;
                buttonData = buttonData || null;
                const template = document.getElementById('slide-button-template');
                const container = document.getElementById('slide-buttons-container');

                if (template && container) {
                    const buttonElement = template.content.cloneNode(true);
                    const buttonItem = buttonElement.querySelector('.slide-button-item');

                    if (buttonData) {
                        const textField = buttonElement.querySelector('.button-text');
                        const actionField = buttonElement.querySelector('.button-action');

                        if (textField) textField.value = buttonData.text || '';
                        if (actionField) {
                            actionField.value = buttonData.action || 'url';
                            // Задаване на стойността според типа действие
                            this.setButtonFieldValue(buttonElement, buttonData.action || 'url', buttonData.value || '');
                        }
                    }

                    // Event listener за промяна на типа действие
                    const actionField = buttonElement.querySelector('.button-action');
                    if (actionField) {
                        actionField.addEventListener('change', function(e) {
                            self.toggleButtonFields(buttonElement, e.target.value);
                        });

                        // Инициализация на полетата според текущия избор
                        this.toggleButtonFields(buttonElement, actionField.value);
                    }

                    // Event listener за премахване
                    const removeBtn = buttonElement.querySelector('.remove-slide-button');
                    if (removeBtn) {
                        removeBtn.addEventListener('click', function(e) {
                            const buttonItem = e.target.closest('.slide-button-item');
                            if (buttonItem) {
                                buttonItem.remove();
                            }
                        });
                    }

                    // Event listeners за page search
                    this.initButtonPageSearch(buttonElement);

                    container.appendChild(buttonElement);
                }
            },

            /**
             * Превключване на полетата според типа действие
             */
            toggleButtonFields: function(buttonElement, actionType) {
                const urlField = buttonElement.querySelector('.button-url-field');
                const pageField = buttonElement.querySelector('.button-page-field');
                const jsField = buttonElement.querySelector('.button-js-field');

                // Скриване на всички полета
                if (urlField) urlField.classList.add('hidden');
                if (pageField) pageField.classList.add('hidden');
                if (jsField) jsField.classList.add('hidden');

                // Показване на съответното поле
                switch (actionType) {
                    case 'url':
                        if (urlField) urlField.classList.remove('hidden');
                        break;
                    case 'page':
                        if (pageField) pageField.classList.remove('hidden');
                        break;
                    case 'javascript':
                        if (jsField) jsField.classList.remove('hidden');
                        break;
                }
            },

            /**
             * Задаване на стойност в полето според типа
             */
            setButtonFieldValue: function(buttonElement, actionType, value) {
                switch (actionType) {
                    case 'url':
                        const urlInput = buttonElement.querySelector('.button-value');
                        if (urlInput) urlInput.value = value;
                        break;
                    case 'page':
                        // За страници ще се имплементира autocomplete
                        if (value) {
                            this.addSelectedPage(buttonElement, value);
                        }
                        break;
                    case 'javascript':
                        const jsTextarea = buttonElement.querySelector('.button-js-code');
                        if (jsTextarea) jsTextarea.value = value;
                        break;
                }
            },

            /**
             * Инициализация на page search за бутон
             */
            initButtonPageSearch: function(buttonElement) {
                const self = this;
                const searchInput = buttonElement.querySelector('.button-page-search');
                const dropdown = buttonElement.querySelector('.button-page-dropdown');

                if (!searchInput || !dropdown) return;

                let searchTimeout;

                // Event listener за input
                searchInput.addEventListener('input', function(e) {
                    const query = e.target.value.trim();

                    clearTimeout(searchTimeout);

                    if (query.length < 2) {
                        dropdown.classList.add('hidden');
                        return;
                    }

                    searchTimeout = setTimeout(() => {
                        self.searchPages(query, dropdown, buttonElement);
                    }, 300);
                });

                // Event listener за фокус - показва първите 10 страници
                searchInput.addEventListener('focus', function(e) {
                    const query = e.target.value.trim();

                    // Ако няма текст или има малко текст, покажи първите 10 страници
                    if (query.length < 2) {
                        self.searchPages('', dropdown, buttonElement, true); // true означава показване на първите страници
                    } else {
                        // Ако има текст, направи търсене
                        self.searchPages(query, dropdown, buttonElement);
                    }
                });

                // Скриване на dropdown при клик извън него
                document.addEventListener('click', function(e) {
                    if (!buttonElement.contains(e.target)) {
                        dropdown.classList.add('hidden');
                    }
                });
            },

            /**
             * Търсене на страници
             */
            searchPages: function(query, dropdown, buttonElement, showFirst10 = false) {
                const self = this;

                // Ако showFirst10 е true и няма query, направи заявка за първите 10 страници
                let url;
                if (showFirst10 && !query) {
                    url = window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/autocomplete&type=pages&limit=10&user_token=' + this.getUserToken();
                } else {
                    url = window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/autocomplete&type=pages&q=' + encodeURIComponent(query) + '&user_token=' + this.getUserToken();
                }

                fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(pages => {
                    dropdown.innerHTML = '';

                    if (pages && pages.length > 0) {
                        pages.forEach(page => {
                            const pageItem = document.createElement('div');
                            pageItem.className = 'px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm';
                            pageItem.textContent = page.name;
                            pageItem.addEventListener('click', function() {
                                self.addSelectedPage(buttonElement, page);
                                dropdown.classList.add('hidden');
                                buttonElement.querySelector('.button-page-search').value = '';
                            });
                            dropdown.appendChild(pageItem);
                        });
                        dropdown.classList.remove('hidden');
                    } else {
                        const message = showFirst10 ? 'Няма налични страници' : 'Няма намерени страници';
                        dropdown.innerHTML = `<div class="px-3 py-2 text-sm text-gray-500">${message}</div>`;
                        dropdown.classList.remove('hidden');
                    }
                })
                .catch(error => {
                    console.error('Page search error:', error);
                    dropdown.classList.add('hidden');
                });
            },

            /**
             * Добавяне на избрана страница
             */
            addSelectedPage: function(buttonElement, page) {
                const selectedPagesContainer = buttonElement.querySelector('.button-selected-pages');
                if (!selectedPagesContainer) return;

                // Премахване на предишни избори (само една страница може да бъде избрана)
                selectedPagesContainer.innerHTML = '';

                const pageTag = document.createElement('div');
                pageTag.className = 'inline-flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs';
                pageTag.innerHTML = `
                    <span>${page.name || page}</span>
                    <button type="button" class="ml-1 text-blue-600 hover:text-blue-800" onclick="this.parentElement.remove()">
                        <i class="ri-close-line"></i>
                    </button>
                `;

                // Скрито поле за стойността
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.className = 'button-page-value';
                hiddenInput.value = page.id || page;
                pageTag.appendChild(hiddenInput);

                selectedPagesContainer.appendChild(pageTag);
            },

            /**
             * Запазване на слайд
             */
            saveSlide: function() {
                const self = this;
                const formData = new FormData(this.slider.slideForm);

                // Добавяне на бутоните
                const buttons = [];
                document.querySelectorAll('.slide-button-item').forEach(item => {
                    const textField = item.querySelector('.button-text');
                    const actionField = item.querySelector('.button-action');

                    const text = textField ? textField.value.trim() : '';
                    const action = actionField ? actionField.value : 'url';

                    if (!text) return; // Пропускаме бутони без текст

                    let value = '';

                    // Получаване на стойността според типа действие
                    switch (action) {
                        case 'url':
                            const urlField = item.querySelector('.button-value');
                            value = urlField ? urlField.value.trim() : '';
                            break;
                        case 'page':
                            const pageValueField = item.querySelector('.button-page-value');
                            value = pageValueField ? pageValueField.value : '';
                            break;
                        case 'javascript':
                            const jsField = item.querySelector('.button-js-code');
                            value = jsField ? jsField.value.trim() : '';
                            break;
                    }

                    if (value) {
                        buttons.push({ text, action, value });
                    }
                });

                formData.append('buttons', JSON.stringify(buttons));
                formData.append('action', this.slider.currentSlideId ? 'edit' : 'add');
                formData.append('tab', 'slider');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                        self.closeSlideModal();

                        // Презареждане на страницата за обновяване на списъка
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Save slide error:', error);
                    self.showAlert('error', 'Възникна грешка при запазването на слайда.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Запазване на настройките на слайдера
             */
            saveSliderSettings: function() {
                const self = this;
                const formData = new FormData(this.slider.sliderSettingsForm);
                formData.append('action', 'settings');
                formData.append('tab', 'slider');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Save settings error:', error);
                    self.showAlert('error', 'Възникна грешка при запазването на настройките.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Отваряне на image manager за слайдера
             */
            openSliderImageManager: function(type) {
                const self = this;
                const targetContainer = document.getElementById('slide-image-container');

                BackendModule.openImageManager({
                    singleSelection: true,
                    startDirectory: '',
                    target: targetContainer,
                    callback: function(selectedImages, target) {
                        if (selectedImages && selectedImages.length > 0 && target && type === 'slide') {
                            const selectedImage = selectedImages[0];
                            const imageInput = document.getElementById('slide-image-input');
                            const imgElement = target.querySelector('img');

                            if (imageInput) {
                                imageInput.value = selectedImage.path;
                            }

                            if (imgElement) {
                                imgElement.src = selectedImage.thumb;
                            }

                            self.updateSlideImagePreview(selectedImage.thumb);
                            self.showAlert('success', 'Изображението е успешно избрано.');
                        }
                    }
                });
            },

            /**
             * Инициализация на подобрена sortable функционалност за слайдовете
             */
            initSliderSortable: function() {
                const self = this;
                const slidesContainer = document.getElementById('slides-container');

                if (!slidesContainer) return;

                // Инициализация на drag & drop променливи
                this.slider.dragState = {
                    draggedElement: null,
                    dragPlaceholder: null,
                    mouseOffsetY: 0,
                    originalIndex: -1
                };

                // Добавяне на drag handle към слайдовете
                this.addDragHandlesToSlides();

                // Bind методи за event listeners
                this.boundOnMouseDown = this.onSlideMouseDown.bind(this);
                this.boundOnMouseMove = this.onSlideMouseMove.bind(this);
                this.boundOnMouseUp = this.onSlideMouseUp.bind(this);

                // Добавяне на event listener за mousedown
                document.addEventListener('mousedown', this.boundOnMouseDown);
            },

            /**
             * Добавяне на drag handles към слайдовете
             */
            addDragHandlesToSlides: function() {
                const slides = document.querySelectorAll('.slide-item');
                slides.forEach(slide => {
                    // Проверка дали вече има drag handle
                    if (slide.querySelector('.drag-handle')) return;

                    // Добавяне на drag handle
                    const dragHandle = document.createElement('div');
                    dragHandle.className = 'drag-handle absolute top-2 left-2 p-2 bg-white/90 rounded-lg text-gray-600 opacity-0 group-hover:opacity-100 transition-all duration-200 cursor-move shadow-sm hover:shadow-md';
                    dragHandle.innerHTML = '<i class="ri-drag-move-line text-lg"></i>';
                    dragHandle.title = 'Влачете за пренареждане';

                    const imageContainer = slide.querySelector('.relative');
                    if (imageContainer) {
                        imageContainer.classList.add('group');
                        imageContainer.appendChild(dragHandle);
                    }
                });
            },

            /**
             * Обработка на mousedown за започване на drag операция
             */
            onSlideMouseDown: function(e) {
                if (this.slider.dragState.draggedElement || e.button !== 0 || !e.target.closest('.drag-handle')) {
                    return;
                }

                e.preventDefault();
                e.stopPropagation();

                this.slider.dragState.draggedElement = e.target.closest('.slide-item');
                if (!this.slider.dragState.draggedElement) return;

                const slidesContainer = document.getElementById('slides-container');
                if (!slidesContainer) return;

                // Запазване на оригиналния индекс
                this.slider.dragState.originalIndex = Array.from(slidesContainer.children).indexOf(this.slider.dragState.draggedElement);

                this.prepareSlideForDrag(e);

                document.addEventListener('mousemove', this.boundOnMouseMove);
                document.addEventListener('mouseup', this.boundOnMouseUp);
            },

            /**
             * Подготовка на слайда за drag операция
             */
            prepareSlideForDrag: function(e) {
                const element = this.slider.dragState.draggedElement;
                const rect = element.getBoundingClientRect();

                // Създаване на placeholder
                this.slider.dragState.dragPlaceholder = document.createElement('div');
                this.slider.dragState.dragPlaceholder.className = 'slide-drag-placeholder border-2 border-dashed border-blue-400 bg-blue-50/50 rounded-lg';
                this.slider.dragState.dragPlaceholder.style.height = `${rect.height}px`;
                this.slider.dragState.dragPlaceholder.style.marginBottom = '1rem';

                // Добавяне на визуален индикатор в placeholder
                this.slider.dragState.dragPlaceholder.innerHTML = `
                    <div class="flex items-center justify-center h-full text-blue-500">
                        <div class="text-center">
                            <i class="ri-drag-drop-line text-2xl mb-2"></i>
                            <p class="text-sm">Пуснете тук</p>
                        </div>
                    </div>
                `;

                element.after(this.slider.dragState.dragPlaceholder);

                // Стилизиране на влачения елемент
                element.style.position = 'absolute';
                element.style.top = `${element.offsetTop}px`;
                element.style.left = `${element.offsetLeft}px`;
                element.style.width = `${rect.width}px`;
                element.style.zIndex = '1000';
                element.style.opacity = '1'; // 100% прозрачност
                element.style.transform = 'rotate(2deg)'; // Лек наклон
                element.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
                element.classList.add('is-dragging');

                // Добавяне на пунктирано очертание
                element.style.border = '2px dashed #3b82f6';
                element.style.borderRadius = '8px';

                this.slider.dragState.mouseOffsetY = e.clientY - rect.top;
            },

            /**
             * Обработка на mousemove по време на drag операция
             */
            onSlideMouseMove: function(e) {
                const element = this.slider.dragState.draggedElement;
                if (!element) return;

                const slidesContainer = document.getElementById('slides-container');
                if (!slidesContainer) return;

                const containerRect = slidesContainer.getBoundingClientRect();

                // Обновяване на позицията на влачения елемент
                let newTop = e.clientY - containerRect.top - this.slider.dragState.mouseOffsetY;
                element.style.top = `${newTop}px`;

                // Намиране на най-близкия слайд за замяна
                const slides = Array.from(slidesContainer.querySelectorAll('.slide-item:not(.is-dragging)'));
                let closestSlide = null;
                let closestDistance = Infinity;

                slides.forEach(slide => {
                    const slideRect = slide.getBoundingClientRect();
                    const slideCenter = slideRect.top + slideRect.height / 2;
                    const distance = Math.abs(e.clientY - slideCenter);

                    if (distance < closestDistance) {
                        closestDistance = distance;
                        closestSlide = slide;
                    }
                });

                // Преместване на placeholder
                if (closestSlide && this.slider.dragState.dragPlaceholder) {
                    const closestRect = closestSlide.getBoundingClientRect();
                    const mouseY = e.clientY;

                    if (mouseY < closestRect.top + closestRect.height / 2) {
                        // Вмъкване преди елемента
                        closestSlide.before(this.slider.dragState.dragPlaceholder);
                    } else {
                        // Вмъкване след елемента
                        closestSlide.after(this.slider.dragState.dragPlaceholder);
                    }
                }
            },

            /**
             * Обработка на mouseup за завършване на drag операция
             */
            onSlideMouseUp: function(e) {
                const element = this.slider.dragState.draggedElement;
                if (!element) return;

                // Премахване на event listeners
                document.removeEventListener('mousemove', this.boundOnMouseMove);
                document.removeEventListener('mouseup', this.boundOnMouseUp);

                // Възстановяване на стиловете на елемента
                element.style.position = '';
                element.style.top = '';
                element.style.left = '';
                element.style.width = '';
                element.style.zIndex = '';
                element.style.opacity = '';
                element.style.transform = '';
                element.style.boxShadow = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.classList.remove('is-dragging');

                // Поставяне на елемента на новата позиция
                if (this.slider.dragState.dragPlaceholder) {
                    this.slider.dragState.dragPlaceholder.before(element);
                    this.slider.dragState.dragPlaceholder.remove();
                }

                // Проверка дали позицията е променена
                const slidesContainer = document.getElementById('slides-container');
                const newIndex = Array.from(slidesContainer.children).indexOf(element);

                if (newIndex !== this.slider.dragState.originalIndex) {
                    // Изпращане на новия ред към сървъра
                    this.updateSliderOrder();
                }

                // Изчистване на drag state
                this.slider.dragState = {
                    draggedElement: null,
                    dragPlaceholder: null,
                    mouseOffsetY: 0,
                    originalIndex: -1
                };
            },

            /**
             * Обновяване на реда на слайдовете след drag & drop
             */
            updateSliderOrder: function() {
                const self = this;
                const slidesContainer = document.getElementById('slides-container');

                if (!slidesContainer) return;

                const slideIds = [];
                slidesContainer.querySelectorAll('.slide-item').forEach(slide => {
                    const slideId = slide.getAttribute('data-slide-id');
                    if (slideId) {
                        slideIds.push(slideId);
                    }
                });

                if (slideIds.length === 0) return;

                const formData = new FormData();
                formData.append('order', JSON.stringify(slideIds));
                formData.append('tab', 'slider');
                formData.append('user_token', this.getUserToken());

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/reorder', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', 'Редът на слайдовете е променен успешно.');
                    } else {
                        self.showAlert('error', result.message || 'Грешка при пренареждане на слайдовете.');
                        // Презареждане на страницата при грешка
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                })
                .catch(error => {
                    console.error('Reorder error:', error);
                    self.showAlert('error', 'Възникна грешка при пренареждането.');
                    // Презареждане на страницата при грешка
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                });
            },

            /**
             * Редактиране на слайд
             */
            editSlide: function(slideId) {
                const self = this;

                if (!slideId) {
                    self.showAlert('error', 'Невалиден ID на слайд.');
                    return;
                }

                // Показване на loading индикатор
                this.showThemeLoading();

                // AJAX заявка за получаване на данните на слайда
                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/autocomplete&type=slide&id=' + slideId + '&user_token=' + this.getUserToken(), {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(slideData => {
                    if (slideData && slideData.id) {
                        // Отваряне на модала с данните на слайда
                        self.openSlideModal(slideData);
                    } else {
                        self.showAlert('error', 'Слайдът не е намерен или възникна грешка при зареждането.');
                    }
                })
                .catch(error => {
                    console.error('Edit slide error:', error);
                    self.showAlert('error', 'Възникна грешка при зареждането на данните на слайда.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Изтриване на слайд
             */
            deleteSlide: function(slideId) {
                const self = this;

                if (!slideId) {
                    self.showAlert('error', 'Невалиден ID на слайд.');
                    return;
                }

                // Потвърждение за изтриване
                if (!confirm('Сигурни ли сте, че искате да изтриете този слайд?')) {
                    return;
                }

                // Показване на loading индикатор
                this.showThemeLoading();

                const formData = new FormData();
                formData.append('id', slideId);
                formData.append('tab', 'slider');
                formData.append('user_token', this.getUserToken());

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/delete', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message || 'Слайдът е изтрит успешно.');

                        // Премахване на слайда от DOM
                        const slideElement = document.querySelector(`[data-slide-id="${slideId}"]`);
                        if (slideElement) {
                            slideElement.remove();
                        }

                        // Проверка дали има останали слайдове
                        const remainingSlides = document.querySelectorAll('.slide-item');
                        if (remainingSlides.length === 0) {
                            // Показване на съобщение за липса на слайдове
                            const slidesContainer = document.getElementById('slides-container');
                            if (slidesContainer) {
                                slidesContainer.innerHTML = `
                                    <div class="text-center py-12">
                                        <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-gray-400">
                                            <i class="ri-image-add-line ri-3x"></i>
                                        </div>
                                        <h3 class="text-gray-600 font-medium mb-2">Все още няма добавени слайдове</h3>
                                        <p class="text-sm text-gray-500 mb-4">Добавете първия слайд като натиснете бутона по-горе</p>
                                    </div>
                                `;
                            }
                        }
                    } else {
                        self.showAlert('error', result.message || 'Възникна грешка при изтриването на слайда.');
                    }
                })
                .catch(error => {
                    console.error('Delete slide error:', error);
                    self.showAlert('error', 'Възникна грешка при изтриването на слайда.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Преглед на слайд
             */
            previewSlide: function(imageSrc) {
                if (!imageSrc) {
                    this.showAlert('error', 'Няма изображение за преглед.');
                    return;
                }

                // Създаване на модал за преглед ако не съществува
                let previewModal = document.getElementById('slide-preview-modal');
                if (!previewModal) {
                    previewModal = document.createElement('div');
                    previewModal.id = 'slide-preview-modal';
                    previewModal.className = 'fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center hidden';
                    previewModal.innerHTML = `
                        <div class="relative max-w-4xl max-h-[90vh] mx-4">
                            <button class="absolute top-4 right-4 p-2 bg-white rounded-full text-gray-700 hover:text-gray-900 z-10" onclick="BackendModule.closeSlidePreview()">
                                <div class="w-6 h-6 flex items-center justify-center">
                                    <i class="ri-close-line"></i>
                                </div>
                            </button>
                            <img id="slide-preview-image" src="" alt="Slide preview" class="max-w-full max-h-full object-contain rounded-lg">
                        </div>
                    `;
                    document.body.appendChild(previewModal);

                    // Затваряне при клик извън изображението
                    previewModal.addEventListener('click', function(e) {
                        if (e.target === previewModal) {
                            BackendModule.closeSlidePreview();
                        }
                    });
                }

                // Задаване на изображението и показване на модала
                const previewImage = document.getElementById('slide-preview-image');
                if (previewImage) {
                    previewImage.src = imageSrc;
                    previewModal.classList.remove('hidden');
                }
            },

            /**
             * Затваряне на модала за преглед на слайд
             */
            closeSlidePreview: function() {
                const previewModal = document.getElementById('slide-preview-modal');
                if (previewModal) {
                    previewModal.classList.add('hidden');
                }
            },

            /**
             * Преместване на слайд
             */
            moveSlide: function(slideItem, direction) {
                if (!slideItem || !direction) {
                    this.showAlert('error', 'Невалидни параметри за преместване.');
                    return;
                }

                const slidesContainer = document.getElementById('slides-container');
                if (!slidesContainer) return;

                const slides = Array.from(slidesContainer.querySelectorAll('.slide-item'));
                const currentIndex = slides.indexOf(slideItem);

                if (currentIndex === -1) {
                    this.showAlert('error', 'Слайдът не е намерен.');
                    return;
                }

                let targetIndex;

                if (direction === 'up') {
                    if (currentIndex === 0) {
                        this.showAlert('info', 'Слайдът вече е на първо място.');
                        return;
                    }
                    targetIndex = currentIndex - 1;
                } else if (direction === 'down') {
                    if (currentIndex === slides.length - 1) {
                        this.showAlert('info', 'Слайдът вече е на последно място.');
                        return;
                    }
                    targetIndex = currentIndex + 1;
                } else {
                    this.showAlert('error', 'Невалидна посока за преместване.');
                    return;
                }

                // Преместване в DOM
                const targetSlide = slides[targetIndex];
                if (direction === 'up') {
                    slidesContainer.insertBefore(slideItem, targetSlide);
                } else {
                    slidesContainer.insertBefore(slideItem, targetSlide.nextSibling);
                }

                // Обновяване на реда в базата данни
                this.updateSliderOrder();
            }
        });
    }

})();
