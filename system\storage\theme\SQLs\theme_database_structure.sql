-- ============================================================================
-- SQL скрипт за създаване на таблици за модул "Тема"
-- Версия: 1.0.0
-- Дата: 2025-07-29
-- ============================================================================

-- Таблица за настройки на слайдера
CREATE TABLE IF NOT EXISTS `oc_theme_slider_settings` (
  `id` int(11) NOT NULL DEFAULT 1,
  `auto_play` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Автоматично превъртане',
  `interval` int(11) NOT NULL DEFAULT 5 COMMENT 'Интервал в секунди',
  `animation` varchar(20) NOT NULL DEFAULT 'fade' COMMENT 'Тип анимация: fade, slide, zoom',
  `show_arrows` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Показване на стрелки',
  `show_dots` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Показване на точки',
  `mobile_view` varchar(20) NOT NULL DEFAULT 'show' COMMENT 'Мобилен изглед: show, hide, single',
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Настройки на слайдера';

-- Таблица за слайдове
CREATE TABLE IF NOT EXISTS `oc_theme_slider_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT 'Заглавие на слайда',
  `subtitle` text NOT NULL COMMENT 'Подзаглавие/описание',
  `image` varchar(500) NOT NULL DEFAULT '' COMMENT 'Път към изображението',
  `buttons` text NOT NULL COMMENT 'JSON данни за бутоните',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Статус: 0=неактивен, 1=активен',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'Ред на показване',
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Слайдове за началната страница';

-- Таблица за банери
CREATE TABLE IF NOT EXISTS `oc_theme_banners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT 'Заглавие на банера',
  `image` varchar(500) NOT NULL DEFAULT '' COMMENT 'Път към изображението',
  `link` varchar(500) NOT NULL DEFAULT '' COMMENT 'Персонализиран линк',
  `page_id` int(11) NOT NULL DEFAULT 0 COMMENT 'ID на страница от information таблицата',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Статус: 0=неактивен, 1=активен',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'Ред на показване',
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_page_id` (`page_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Банери за сайта';

-- ============================================================================
-- Примерни данни за тестване
-- ============================================================================

-- Вмъкване на примерни настройки за слайдера
INSERT INTO `oc_theme_slider_settings` (`id`, `auto_play`, `interval`, `animation`, `show_arrows`, `show_dots`, `mobile_view`) 
VALUES (1, 1, 5, 'fade', 1, 1, 'show')
ON DUPLICATE KEY UPDATE 
  `auto_play` = VALUES(`auto_play`),
  `interval` = VALUES(`interval`),
  `animation` = VALUES(`animation`),
  `show_arrows` = VALUES(`show_arrows`),
  `show_dots` = VALUES(`show_dots`),
  `mobile_view` = VALUES(`mobile_view`);

-- Вмъкване на примерни слайдове (опционално)
-- INSERT INTO `{{prefix}}theme_slider_items` (`title`, `subtitle`, `image`, `buttons`, `status`, `sort_order`) VALUES
-- ('Добре дошли в нашия магазин', 'Открийте най-новите продукти и специални оферти', 'catalog/slider/slide1.jpg', '[{"text":"Разгледай","action":"url","value":"/catalog"}]', 1, 1),
-- ('Специални оферти', 'До 50% отстъпка на избрани продукти', 'catalog/slider/slide2.jpg', '[{"text":"Виж офертите","action":"url","value":"/special"}]', 1, 2),
-- ('Нова колекция', 'Разгледайте нашата най-нова колекция', 'catalog/slider/slide3.jpg', '[{"text":"Разгледай","action":"url","value":"/new-collection"}]', 1, 3);

-- Вмъкване на примерни банери (опционално)
-- INSERT INTO `{{prefix}}theme_banners` (`title`, `image`, `link`, `page_id`, `status`, `sort_order`) VALUES
-- ('Промоционален банер 1', 'catalog/banners/banner1.jpg', '', 4, 1, 1),
-- ('Промоционален банер 2', 'catalog/banners/banner2.jpg', 'https://example.com/promo', 0, 1, 2),
-- ('Промоционален банер 3', 'catalog/banners/banner3.jpg', '', 5, 1, 3);

-- ============================================================================
-- Индекси за оптимизация
-- ============================================================================

-- Допълнителни индекси за по-добра производителност
ALTER TABLE `oc_theme_slider_items` 
ADD INDEX `idx_status_sort` (`status`, `sort_order`);

ALTER TABLE `oc_theme_banners` 
ADD INDEX `idx_status_sort` (`status`, `sort_order`);

-- ============================================================================
-- Права за достъп (за OpenCart user_group таблицата)
-- ============================================================================

-- Добавяне на права за достъп до модула "Тема" за администраторската група
-- Тези заявки трябва да се изпълнят ръчно или чрез инсталационен скрипт

-- INSERT INTO `{{prefix}}user_group_permission` (`user_group_id`, `permission`, `type`) VALUES
-- (1, 'system/theme', 'access'),
-- (1, 'system/theme', 'modify');

-- ============================================================================
-- Коментари и документация
-- ============================================================================

/*
СТРУКТУРА НА ДАННИТЕ:

1. theme_slider_settings
   - Съхранява глобалните настройки на слайдера
   - Има само един ред с id=1
   - Всички настройки са с default стойности

2. theme_slider_items
   - Съхранява отделните слайдове
   - buttons полето съдържа JSON с масив от бутони
   - sort_order определя реда на показване
   - status контролира видимостта

3. theme_banners
   - Съхранява банерите за сайта
   - Може да има или link (персонализиран) или page_id (от information таблицата)
   - sort_order определя реда на показване
   - status контролира видимостта

JSON СТРУКТУРА ЗА БУТОНИ В СЛАЙДОВЕТЕ:
[
  {
    "text": "Текст на бутона",
    "action": "url|page|javascript",
    "value": "стойност според action"
  }
]

ПРИМЕРИ ЗА action:
- "url": value е URL адрес
- "page": value е ID на страница от information таблицата  
- "javascript": value е JavaScript код за изпълнение
*/
