<?php

namespace Theme25\Backend\Controller\System\Theme;

/**
 * Суб-контролер за управление на банерите
 *
 * @package Theme25\Backend\Controller\System\Theme
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Banners extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);

        $this->loadThemeModel();
    }

    /**
     * Подготовка на данните за банерите
     */
    public function prepareData() {
        $this->prepareBannerItems()
             ->prepareBannerLinks();
        
        return $this;
    }

    /**
     * Зареждане на модела за темата
     */
    private function loadThemeModel() {
        $this->loadModelAs('system/theme', 'themeModel');
        return $this;
    }

    /**
     * Подготовка на банерите
     */
    private function prepareBannerItems() {
        $banners = $this->themeModel->getBannerItems();

        // Зареждане на модела за страници
        $this->loadModelsAs(['catalog/information' => 'informationModel']);

        // Подготовка на данните за всеки банер
        $prepared_banners = [];
        foreach ($banners as $banner) {
            $page_name = '';

            // Получаване на името на страницата ако има page_id
            if (!empty($banner['page_id'])) {
                $page_info = $this->informationModel->getInformation($banner['page_id']);

                F()->log->developer('Page info: ' . print_r($page_info, true), __FILE__, __LINE__);


                $page_name = $page_info ? $page_info['title'] : 'Неизвестна страница';
            }

            $prepared_banners[] = [
                'id' => $banner['id'],
                'title' => $banner['title'],
                'image' => $banner['image'],
                'image_url' => $this->getImageUrl($banner['image']),
                'link' => $banner['link'],
                'page_id' => $banner['page_id'],
                'page_name' => $page_name,
                'status' => $banner['status'],
                'sort_order' => $banner['sort_order']
            ];
        }

        $this->setData('banner_items', $prepared_banners);

        return $this;
    }

    /**
     * Подготовка на линковете за банерите
     */
    private function prepareBannerLinks() {
        $links = [
            'add_banner' => $this->getAdminLink('system/theme/save', 'tab=banners&action=add'),
            'delete_banner' => $this->getAdminLink('system/theme/delete', 'tab=banners'),
            'save_banner' => $this->getAdminLink('system/theme/save', 'tab=banners')
        ];

        $this->setData('banner_links', $links);

        return $this;
    }

    /**
     * Запазване на банер
     */
    public function save($data) {
        $json = ['success' => false, 'message' => ''];
        
        try {
            $this->saveBannerItem($data);
            $json = ['success' => true, 'message' => 'Банерът е запазен успешно.'];
            
        } catch (Exception $e) {
            $json['message'] = 'Грешка при запазване: ' . $e->getMessage();
        }
        
        return $json;
    }

    /**
     * Запазване на банер
     */
    private function saveBannerItem($data) {
        // Обработка на link_type
        $link = '';
        $page_id = 0;

        $link_type = $data['link_type'] ?? 'page';

        if ($link_type === 'url') {
            $link = $data['link'] ?? '';
            $page_id = 0;
        } elseif ($link_type === 'page') {
            $link = '';
            $page_id = (int)($data['page_id'] ?? 0);
        }

        $banner_data = [
            'title' => $data['title'] ?? '',
            'image' => $data['image'] ?? '',
            'link' => $link,
            'page_id' => $page_id,
            'status' => isset($data['status']) ? 1 : 0
        ];

        if (isset($data['banner_id']) && $data['banner_id'] > 0) {
            $this->themeModel->updateBannerItem($data['banner_id'], $banner_data);
        } else {
            $this->themeModel->addBannerItem($banner_data);
        }
    }

    /**
     * Изтриване на банер
     */
    public function delete($id) {
        $json = ['success' => false, 'message' => ''];

        try {
            $this->themeModel->deleteBannerItem($id);
            $json = ['success' => true, 'message' => 'Банерът е изтрит успешно.'];
        } catch (Exception $e) {
            $json['message'] = 'Грешка при изтриване: ' . $e->getMessage();
        }

        return $json;
    }

    /**
     * Генериране на URL за изображение
     */
    private function getImageUrl($image) {
        if (empty($image)) {
            return '';
        }
        
        // Използваме \Theme25\Data за получаване на URL-а на изображението
        return \Theme25\Data::getInstance()->getImageWebUrl() . $image;
    }
}
