<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-800">Банери</h3>
        <button id="add-banner-button" class="px-3 py-1.5 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm flex items-center !rounded-button">
            <div class="w-4 h-4 flex items-center justify-center mr-1">
                <i class="ri-add-line"></i>
            </div>
            <span>Добави нов банер</span>
        </button>
    </div>

    <div id="banners-container">
        {% if banner_items %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for banner in banner_items %}
                <div class="banner-item bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm" data-banner-id="{{ banner.id }}">
                    <div class="relative">
                        <img src="{{ banner.image_url }}" alt="Banner image" class="w-full h-48 object-cover">
                        <div class="absolute top-2 right-2">
                            <div class="flex items-center space-x-1">
                                {% if banner.status %}
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Активен</span>
                                {% else %}
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">Неактивен</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                            <button class="preview-banner p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Преглед">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-eye-line"></i>
                                </div>
                            </button>
                            <button class="edit-banner p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Редактиране">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-edit-line"></i>
                                </div>
                            </button>
                            <button class="delete-banner p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Изтриване">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-delete-bin-line"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        <h4 class="font-medium text-gray-800 mb-1">{{ banner.title }}</h4>
                        {% if banner.link %}
                            <p class="text-sm text-gray-500 truncate" title="{{ banner.link }}">{{ banner.link }}</p>
                        {% elseif banner.page_name %}
                            <p class="text-sm text-blue-600 truncate" title="Страница: {{ banner.page_name }}">
                                <i class="ri-file-text-line mr-1"></i>{{ banner.page_name }}
                            </p>
                        {% else %}
                            <p class="text-sm text-gray-400">Без линк</p>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-gray-400">
                    <i class="ri-image-add-line ri-3x"></i>
                </div>
                <h3 class="text-gray-600 font-medium mb-2">Все още няма добавени банери</h3>
                <p class="text-sm text-gray-500 mb-4">Добавете първия банер като натиснете бутона по-горе</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Banner Modal -->
<div id="banner-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-800" id="banner-modal-title">Добавяне на банер</h3>
            <button class="close-banner-modal text-gray-400 hover:text-gray-600">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-close-line"></i>
                </div>
            </button>
        </div>
        <form id="banner-form" class="p-4">
            <input type="hidden" name="banner_id" id="banner-id">
            <div class="space-y-4">
                <div class="flex items-center mb-4">
                    <input type="checkbox" name="status" id="banner-status" class="mr-2">
                    <label for="banner-status" class="text-sm font-medium text-gray-700">Активен банер</label>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Изображение</label>
                    <div id="banner-image-container" class="max-w-xs">
                        <div id="banner-image-placeholder" class="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" style="width: 300px; height: 150px;">
                            <div class="flex items-center space-x-2 mb-2">
                                <button type="button" data-action="select-banner-image" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                                    <i class="ri-folder-image-line ri-lg"></i>
                                </button>
                            </div>
                            <p class="text-xs text-gray-400 text-center">Няма изображение</p>
                        </div>
                    </div>
                    <input type="hidden" name="image" id="banner-image-input">
                    <p class="text-xs text-gray-500 mt-2">Предефинирани размери: 300x250px, 600x300px, 728x90px. Поддържани формати: JPG, PNG, GIF</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Заглавие на банера</label>
                    <input type="text" name="title" id="banner-title" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете заглавие на банера">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Препратка</label>
                    <div class="space-y-4">
                        <div class="banner-page-option">
                            <label class="flex items-center">
                                <input type="radio" name="link_type" value="page" class="mr-2" checked>
                                <span class="text-sm text-gray-700">Избор на страница</span>
                            </label>
                            <div class="mt-3 ml-6">
                                <div class="banner-selected-pages mb-2">
                                    <!-- Selected pages will appear here -->
                                </div>
                                <div class="relative">
                                    <input type="text" id="banner-page-search" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Търсене на страници...">
                                    <div id="banner-page-dropdown" class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-b shadow-lg max-h-48 overflow-y-auto hidden z-10">
                                        <!-- Search results will appear here -->
                                    </div>
                                </div>
                                <input type="hidden" name="page_id" id="banner-page-id">
                            </div>
                        </div>

                        <div class="banner-url-option">
                            <label class="flex items-center">
                                <input type="radio" name="link_type" value="url" class="mr-2">
                                <span class="text-sm text-gray-700">Персонализиран линк</span>
                            </label>
                            <div class="mt-3 ml-6">
                                <input type="text" name="link" id="banner-link" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="https://example.com" disabled>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Размер на банера</label>
                    <select name="banner_size" id="banner-size" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                        <option value="300x250">Среден правоъгълник (300x250px)</option>
                        <option value="600x300">Голям банер (600x300px)</option>
                        <option value="728x90">Leaderboard (728x90px)</option>
                        <option value="320x50">Мобилен банер (320x50px)</option>
                        <option value="custom">Персонализиран размер</option>
                    </select>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" class="close-banner-modal px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors text-sm !rounded-button">Отказ</button>
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm !rounded-button">Запази</button>
            </div>
        </form>
    </div>
</div>

<!-- Preview Modal -->
<div id="banner-preview-modal" class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center hidden">
    <div class="relative w-full h-full flex items-center justify-center p-4">
        <button class="close-banner-preview absolute top-4 right-4 text-white hover:text-gray-300">
            <div class="w-8 h-8 flex items-center justify-center">
                <i class="ri-close-line ri-2x"></i>
            </div>
        </button>
        <img id="banner-preview-image" src="" alt="Preview" class="max-w-full max-h-full object-contain">
    </div>
</div>
