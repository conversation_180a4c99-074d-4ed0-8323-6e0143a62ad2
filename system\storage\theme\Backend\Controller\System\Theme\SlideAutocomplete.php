<?php

namespace Theme25\Backend\Controller\System\Theme;

/**
 * Суб-контролер за автодопълване на слайдове
 *
 * @package Theme25\Backend\Controller\System\Theme
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class SlideAutocomplete extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->loadThemeModel();
    }

    /**
     * Зареждане на модела за темата
     */
    private function loadThemeModel() {
        $this->loadModelAs('system/theme', 'themeModel');
    }

    /**
     * Автодопълване за слайдове
     */
    public function autocomplete($params) {
        $json = [];

        if (isset($params['id']) && $params['id'] > 0) {
            // Получаване на данни за конкретен слайд
            $slide = $this->themeModel->getSliderItem($params['id']);
            
            if ($slide) {
                $json = [
                    'id' => $slide['id'],
                    'title' => $slide['title'],
                    'subtitle' => $slide['subtitle'],
                    'image' => $slide['image'],
                    'image_url' => $this->getImageUrl($slide['image']),
                    'buttons' => json_decode($slide['buttons'], true) ?: [],
                    'status' => (bool)$slide['status']
                ];
            }
        }

        return $json;
    }

    /**
     * Генериране на URL за изображение
     */
    private function getImageUrl($image) {
        if (empty($image)) {
            return '';
        }
        
        // Използваме \Theme25\Data за получаване на URL-а на изображението
        return \Theme25\Data::getInstance()->getImageWebUrl() . $image;
    }
}
