<?php

namespace Theme25\Backend\Model\System;

/**
 * Модел за управление на темата
 *
 * @package Theme25\Backend\Model\System
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Theme extends \Theme25\Model {

    /**
     * Получаване на настройките на слайдера
     */
    public function getSliderSettings() {
        $query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "theme_slider_settings` LIMIT 1");
        
        if ($query->num_rows) {
            return $query->row;
        }
        
        return [];
    }

    /**
     * Запазване на настройките на слайдера
     */
    public function saveSliderSettings($settings) {
        // Проверка дали има съществуващи настройки
        $existing = $this->dbQuery("SELECT id FROM `" . DB_PREFIX . "theme_slider_settings` LIMIT 1");
        
        if ($existing->num_rows) {
            // Обновяване на съществуващите настройки
            $this->dbQuery("UPDATE `" . DB_PREFIX . "theme_slider_settings` SET 
                auto_play = '" . (int)$settings['auto_play'] . "',
                `interval` = '" . (int)$settings['interval'] . "',
                animation = '" . $this->dbEscape($settings['animation']) . "',
                show_arrows = '" . (int)$settings['show_arrows'] . "',
                show_dots = '" . (int)$settings['show_dots'] . "',
                mobile_view = '" . $this->dbEscape($settings['mobile_view']) . "'
            ");
        } else {
            // Създаване на нови настройки
            $this->dbQuery("INSERT INTO `" . DB_PREFIX . "theme_slider_settings` SET 
                auto_play = '" . (int)$settings['auto_play'] . "',
                `interval` = '" . (int)$settings['interval'] . "',
                animation = '" . $this->dbEscape($settings['animation']) . "',
                show_arrows = '" . (int)$settings['show_arrows'] . "',
                show_dots = '" . (int)$settings['show_dots'] . "',
                mobile_view = '" . $this->dbEscape($settings['mobile_view']) . "'
            ");
        }
    }

    /**
     * Получаване на всички слайдове
     */
    public function getSliderItems() {
        $query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "theme_slider_items` 
            WHERE status = 1 
            ORDER BY sort_order ASC, id ASC");
        
        return $query->rows;
    }

    /**
     * Получаване на конкретен слайд
     */
    public function getSliderItem($id) {
        $query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "theme_slider_items` 
            WHERE id = '" . (int)$id . "'");
        
        return $query->num_rows ? $query->row : false;
    }

    /**
     * Добавяне на нов слайд
     */
    public function addSliderItem($data) {
        $sort_order = $this->getNextSliderSortOrder();
        
        $this->dbQuery("INSERT INTO `" . DB_PREFIX . "theme_slider_items` SET 
            title = '" . $this->dbEscape($data['title']) . "',
            subtitle = '" . $this->dbEscape($data['subtitle']) . "',
            image = '" . $this->dbEscape($data['image']) . "',
            buttons = '" . $this->dbEscape($data['buttons']) . "',
            status = '" . (int)$data['status'] . "',
            sort_order = '" . (int)$sort_order . "',
            date_added = NOW(),
            date_modified = NOW()
        ");
        
        return $this->db->getLastId();
    }

    /**
     * Обновяване на слайд
     */
    public function updateSliderItem($id, $data) {
        $this->dbQuery("UPDATE `" . DB_PREFIX . "theme_slider_items` SET 
            title = '" . $this->dbEscape($data['title']) . "',
            subtitle = '" . $this->dbEscape($data['subtitle']) . "',
            image = '" . $this->dbEscape($data['image']) . "',
            buttons = '" . $this->dbEscape($data['buttons']) . "',
            status = '" . (int)$data['status'] . "',
            date_modified = NOW()
            WHERE id = '" . (int)$id . "'
        ");
    }

    /**
     * Изтриване на слайд
     */
    public function deleteSliderItem($id) {
        $this->dbQuery("DELETE FROM `" . DB_PREFIX . "theme_slider_items` WHERE id = '" . (int)$id . "'");
    }

    /**
     * Пренареждане на слайдовете
     */
    public function reorderSliderItems($order) {
        if (!is_array($order)) {
            $order = json_decode($order, true);
        }
        
        foreach ($order as $index => $id) {
            $this->dbQuery("UPDATE `" . DB_PREFIX . "theme_slider_items` SET 
                sort_order = '" . (int)($index + 1) . "'
                WHERE id = '" . (int)$id . "'
            ");
        }
    }

    /**
     * Получаване на следващия sort_order за слайдовете
     */
    private function getNextSliderSortOrder() {
        $query = $this->dbQuery("SELECT MAX(sort_order) as max_order FROM `" . DB_PREFIX . "theme_slider_items`");
        return ($query->row['max_order'] ?? 0) + 1;
    }

    /**
     * Получаване на всички банери
     */
    public function getBannerItems() {
        $query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "theme_banner_items` 
            WHERE status = 1 
            ORDER BY sort_order ASC, id ASC");
        
        return $query->rows;
    }

    /**
     * Получаване на конкретен банер
     */
    public function getBannerItem($id) {
        $query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "theme_banner_items` 
            WHERE id = '" . (int)$id . "'");
        
        return $query->num_rows ? $query->row : false;
    }

    /**
     * Добавяне на нов банер
     */
    public function addBannerItem($data) {
        $sort_order = $this->getNextBannerSortOrder();
        
        $this->dbQuery("INSERT INTO `" . DB_PREFIX . "theme_banner_items` SET 
            title = '" . $this->dbEscape($data['title']) . "',
            image = '" . $this->dbEscape($data['image']) . "',
            link = '" . $this->dbEscape($data['link']) . "',
            page_id = '" . (int)$data['page_id'] . "',
            status = '" . (int)$data['status'] . "',
            sort_order = '" . (int)$sort_order . "',
            date_added = NOW(),
            date_modified = NOW()
        ");
        
        return $this->db->getLastId();
    }

    /**
     * Обновяване на банер
     */
    public function updateBannerItem($id, $data) {
        $this->dbQuery("UPDATE `" . DB_PREFIX . "theme_banner_items` SET 
            title = '" . $this->dbEscape($data['title']) . "',
            image = '" . $this->dbEscape($data['image']) . "',
            link = '" . $this->dbEscape($data['link']) . "',
            page_id = '" . (int)$data['page_id'] . "',
            status = '" . (int)$data['status'] . "',
            date_modified = NOW()
            WHERE id = '" . (int)$id . "'
        ");
    }

    /**
     * Изтриване на банер
     */
    public function deleteBannerItem($id) {
        $this->dbQuery("DELETE FROM `" . DB_PREFIX . "theme_banner_items` WHERE id = '" . (int)$id . "'");
    }

    /**
     * Получаване на следващия sort_order за банерите
     */
    private function getNextBannerSortOrder() {
        $query = $this->dbQuery("SELECT MAX(sort_order) as max_order FROM `" . DB_PREFIX . "theme_banner_items`");
        return ($query->row['max_order'] ?? 0) + 1;
    }
}
