<?php

namespace Theme25\Model\System;

/**
 * Модел за работа с настройките на темата
 *
 * @package Theme25\Model\System
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Theme extends \Theme25\Model {

    /**
     * Получаване на настройките на слайдера
     */
    public function getSliderSettings() {
        $query = $this->dbQuery("
            SELECT * FROM `" . DB_PREFIX . "theme_slider_settings` 
            WHERE `id` = 1
        ");

        if ($query->num_rows) {
            return $query->row;
        }

        return [];
    }

    /**
     * Запазване на настройките на слайдера
     */
    public function saveSliderSettings($data) {
        $this->dbQuery("
            INSERT INTO `" . DB_PREFIX . "theme_slider_settings` 
            SET `id` = 1,
                `auto_play` = '" . (int)$data['auto_play'] . "',
                `interval` = '" . (int)$data['interval'] . "',
                `animation` = '" . $this->dbEscape($data['animation']) . "',
                `show_arrows` = '" . (int)$data['show_arrows'] . "',
                `show_dots` = '" . (int)$data['show_dots'] . "',
                `mobile_view` = '" . $this->dbEscape($data['mobile_view']) . "'
            ON DUPLICATE KEY UPDATE
                `auto_play` = '" . (int)$data['auto_play'] . "',
                `interval` = '" . (int)$data['interval'] . "',
                `animation` = '" . $this->dbEscape($data['animation']) . "',
                `show_arrows` = '" . (int)$data['show_arrows'] . "',
                `show_dots` = '" . (int)$data['show_dots'] . "',
                `mobile_view` = '" . $this->dbEscape($data['mobile_view']) . "'
        ");
    }

    /**
     * Получаване на всички слайдове
     */
    public function getSliderItems() {
        $query = $this->dbQuery("
            SELECT * FROM `" . DB_PREFIX . "theme_slider_items` 
            ORDER BY `sort_order` ASC, `id` ASC
        ");

        return $query->rows;
    }

    /**
     * Получаване на слайд по ID
     */
    public function getSliderItem($id) {
        $query = $this->dbQuery("
            SELECT * FROM `" . DB_PREFIX . "theme_slider_items` 
            WHERE `id` = '" . (int)$id . "'
        ");

        return $query->row;
    }

    /**
     * Добавяне на нов слайд
     */
    public function addSliderItem($data) {
        $sort_order = $this->getNextSliderSortOrder();

        $data['buttons'] = html_entity_decode(urldecode($data['buttons']), ENT_QUOTES, 'UTF-8');

        F()->log->developer($data, __FILE__, __LINE__);

        $this->dbQuery("
            INSERT INTO `" . DB_PREFIX . "theme_slider_items` 
            SET `title` = '" . $this->dbEscape($data['title']) . "',
                `subtitle` = '" . $this->dbEscape($data['subtitle']) . "',
                `image` = '" . $this->dbEscape($data['image']) . "',
                `buttons` = '" . $this->dbEscape($data['buttons']) . "',
                `status` = '" . (int)$data['status'] . "',
                `sort_order` = '" . (int)$sort_order . "'
        ");

        return $this->db->getLastId();
    }

    /**
     * Обновяване на слайд
     */
    public function updateSliderItem($id, $data) {

         $data['buttons'] = html_entity_decode(urldecode($data['buttons']), ENT_QUOTES, 'UTF-8');
         
         F()->log->developer($data, __FILE__, __LINE__);

        $this->dbQuery("
            UPDATE `" . DB_PREFIX . "theme_slider_items` 
            SET `title` = '" . $this->dbEscape($data['title']) . "',
                `subtitle` = '" . $this->dbEscape($data['subtitle']) . "',
                `image` = '" . $this->dbEscape($data['image']) . "',
                `buttons` = '" . $this->dbEscape($data['buttons']) . "',
                `status` = '" . (int)$data['status'] . "'
            WHERE `id` = '" . (int)$id . "'
        ");
    }

    /**
     * Изтриване на слайд
     */
    public function deleteSliderItem($id) {
        $this->dbQuery("
            DELETE FROM `" . DB_PREFIX . "theme_slider_items` 
            WHERE `id` = '" . (int)$id . "'
        ");
    }

    /**
     * Пренареждане на слайдовете
     */
    public function reorderSliderItems($order) {
        foreach ($order as $sort_order => $id) {
            $this->dbQuery("
                UPDATE `" . DB_PREFIX . "theme_slider_items` 
                SET `sort_order` = '" . (int)$sort_order . "' 
                WHERE `id` = '" . (int)$id . "'
            ");
        }
    }

    /**
     * Получаване на следващия sort_order за слайдер
     */
    private function getNextSliderSortOrder() {
        $query = $this->dbQuery("
            SELECT MAX(`sort_order`) as max_order 
            FROM `" . DB_PREFIX . "theme_slider_items`
        ");

        return ($query->row['max_order'] ?? 0) + 1;
    }

    /**
     * Получаване на всички банери
     */
    public function getBannerItems() {
        $query = $this->dbQuery("
            SELECT * FROM `" . DB_PREFIX . "theme_banners` 
            ORDER BY `sort_order` ASC, `id` ASC
        ");

        return $query->rows;
    }

    /**
     * Получаване на банер по ID
     */
    public function getBannerItem($id) {
        $query = $this->dbQuery("
            SELECT * FROM `" . DB_PREFIX . "theme_banners` 
            WHERE `id` = '" . (int)$id . "'
        ");

        return $query->row;
    }

    /**
     * Добавяне на нов банер
     */
    public function addBannerItem($data) {
        $sort_order = $this->getNextBannerSortOrder();

        $this->dbQuery("
            INSERT INTO `" . DB_PREFIX . "theme_banners` 
            SET `title` = '" . $this->dbEscape($data['title']) . "',
                `image` = '" . $this->dbEscape($data['image']) . "',
                `link` = '" . $this->dbEscape($data['link']) . "',
                `page_id` = '" . (int)$data['page_id'] . "',
                `status` = '" . (int)$data['status'] . "',
                `sort_order` = '" . (int)$sort_order . "'
        ");

        return $this->db->getLastId();
    }

    /**
     * Обновяване на банер
     */
    public function updateBannerItem($id, $data) {
        $this->dbQuery("
            UPDATE `" . DB_PREFIX . "theme_banners` 
            SET `title` = '" . $this->dbEscape($data['title']) . "',
                `image` = '" . $this->dbEscape($data['image']) . "',
                `link` = '" . $this->dbEscape($data['link']) . "',
                `page_id` = '" . (int)$data['page_id'] . "',
                `status` = '" . (int)$data['status'] . "'
            WHERE `id` = '" . (int)$id . "'
        ");
    }

    /**
     * Изтриване на банер
     */
    public function deleteBannerItem($id) {
        $this->dbQuery("
            DELETE FROM `" . DB_PREFIX . "theme_banners` 
            WHERE `id` = '" . (int)$id . "'
        ");
    }

    /**
     * Получаване на следващия sort_order за банери
     */
    private function getNextBannerSortOrder() {
        $query = $this->dbQuery("
            SELECT MAX(`sort_order`) as max_order 
            FROM `" . DB_PREFIX . "theme_banners`
        ");

        return ($query->row['max_order'] ?? 0) + 1;
    }
}
