/**
 * JavaScript функционалност за управление на банерите в темата
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initThemeBanners();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за банерите
            banners: {
                bannerModal: null,
                previewModal: null,
                currentBannerId: null,
                bannerForm: null,
                bannersContainer: null
            },

            /**
             * Инициализация на функционалността за банерите
             */
            initThemeBanners: function() {
                this.initBannerElements();
                this.initBannerEvents();
            },

            /**
             * Инициализация на елементите за банерите
             */
            initBannerElements: function() {
                this.banners.bannerModal = document.getElementById('banner-modal');
                this.banners.previewModal = document.getElementById('banner-preview-modal');
                this.banners.bannerForm = document.getElementById('banner-form');
                this.banners.bannersContainer = document.getElementById('banners-container');
            },

            /**
             * Свързване на събития за банерите
             */
            initBannerEvents: function() {
                const self = this;

                // Бутон за добавяне на банер
                const addBannerBtn = document.getElementById('add-banner-button');
                if (addBannerBtn) {
                    addBannerBtn.addEventListener('click', function() {
                        self.openBannerModal();
                    });
                }

                // Затваряне на модалите
                document.querySelectorAll('.close-banner-modal').forEach(btn => {
                    btn.addEventListener('click', function() {
                        self.closeBannerModal();
                    });
                });

                document.querySelectorAll('.close-banner-preview').forEach(btn => {
                    btn.addEventListener('click', function() {
                        self.closeBannerPreviewModal();
                    });
                });

                // Клик извън модалите
                if (this.banners.bannerModal) {
                    this.banners.bannerModal.addEventListener('click', function(e) {
                        if (e.target === self.banners.bannerModal) {
                            self.closeBannerModal();
                        }
                    });
                }

                if (this.banners.previewModal) {
                    this.banners.previewModal.addEventListener('click', function(e) {
                        if (e.target === self.banners.previewModal) {
                            self.closeBannerPreviewModal();
                        }
                    });
                }

                // Форма за банер
                if (this.banners.bannerForm) {
                    this.banners.bannerForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveBanner();
                    });
                }

                // Бутони в банерите
                this.initBannerButtons();

                // Избор на изображение
                this.initBannerImageSelection();

                // Радио бутони за тип линк
                this.initBannerLinkTypeRadios();
            },

            /**
             * Свързване на бутоните в банерите
             */
            initBannerButtons: function() {
                const self = this;

                // Редактиране на банер
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.edit-banner')) {
                        e.preventDefault();
                        const bannerItem = e.target.closest('.banner-item');
                        const bannerId = bannerItem.getAttribute('data-banner-id');
                        self.editBanner(bannerId);
                    }
                });

                // Изтриване на банер
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.delete-banner')) {
                        e.preventDefault();
                        const bannerItem = e.target.closest('.banner-item');
                        const bannerId = bannerItem.getAttribute('data-banner-id');
                        self.deleteBanner(bannerId);
                    }
                });

                // Преглед на банер
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.preview-banner')) {
                        e.preventDefault();
                        const bannerItem = e.target.closest('.banner-item');
                        const img = bannerItem.querySelector('img');
                        self.previewBanner(img.src);
                    }
                });
            },

            /**
             * Свързване на избора на изображение за банери
             */
            initBannerImageSelection: function() {
                const self = this;

                document.addEventListener('click', function(e) {
                    if (e.target.closest('[data-action="select-banner-image"]')) {
                        e.preventDefault();
                        self.openBannerImageManager('banner');
                    }
                });
            },

            /**
             * Свързване на радио бутоните за тип линк
             */
            initBannerLinkTypeRadios: function() {
                const self = this;

                document.addEventListener('change', function(e) {
                    if (e.target.name === 'link_type') {
                        self.toggleBannerLinkInputs(e.target.value);
                    }
                });
            },

            /**
             * Превключване на полетата за линк в банерите
             */
            toggleBannerLinkInputs: function(linkType) {
                const pageSelect = document.getElementById('banner-page-select');
                const linkInput = document.getElementById('banner-link');

                if (linkType === 'page') {
                    if (pageSelect) {
                        pageSelect.disabled = false;
                    }
                    if (linkInput) {
                        linkInput.disabled = true;
                        linkInput.value = '';
                    }
                } else if (linkType === 'url') {
                    if (pageSelect) {
                        pageSelect.disabled = true;
                        pageSelect.value = '';
                    }
                    if (linkInput) {
                        linkInput.disabled = false;
                    }
                }
            },

            /**
             * Отваряне на модала за банер
             */
            openBannerModal: function(bannerData) {
                bannerData = bannerData || null;
                this.banners.currentBannerId = bannerData ? bannerData.id : null;

                // Задаване на заглавието
                const title = document.getElementById('banner-modal-title');
                if (title) {
                    title.textContent = bannerData ? 'Редактиране на банер' : 'Добавяне на банер';
                }

                // Попълване на формата
                if (bannerData) {
                    this.populateBannerForm(bannerData);
                } else {
                    this.resetBannerForm();
                }

                // Зареждане на страниците
                this.loadBannerPages();

                // Показване на модала
                if (this.banners.bannerModal) {
                    this.banners.bannerModal.classList.remove('hidden');
                }
            },

            /**
             * Затваряне на модала за банер
             */
            closeBannerModal: function() {
                if (this.banners.bannerModal) {
                    this.banners.bannerModal.classList.add('hidden');
                }
                this.banners.currentBannerId = null;
                this.resetBannerForm();
            },

            /**
             * Затваряне на модала за преглед на банер
             */
            closeBannerPreviewModal: function() {
                if (this.banners.previewModal) {
                    this.banners.previewModal.classList.add('hidden');
                }
            },

            /**
             * Попълване на формата за банер
             */
            populateBannerForm: function(bannerData) {
                const bannerIdField = document.getElementById('banner-id');
                const bannerTitleField = document.getElementById('banner-title');
                const bannerStatusField = document.getElementById('banner-status');
                const bannerImageField = document.getElementById('banner-image-input');

                if (bannerIdField) bannerIdField.value = bannerData.id || '';
                if (bannerTitleField) bannerTitleField.value = bannerData.title || '';
                if (bannerStatusField) bannerStatusField.checked = bannerData.status || false;
                if (bannerImageField) bannerImageField.value = bannerData.image || '';

                // Показване на изображението
                if (bannerData.image_url) {
                    this.updateBannerImagePreview(bannerData.image_url);
                }

                // Настройка на линка
                if (bannerData.page_id) {
                    const pageRadio = document.querySelector('input[name="link_type"][value="page"]');
                    const pageSelect = document.getElementById('banner-page-select');
                    if (pageRadio) pageRadio.checked = true;
                    if (pageSelect) pageSelect.value = bannerData.page_id;
                    this.toggleBannerLinkInputs('page');
                } else if (bannerData.link) {
                    const urlRadio = document.querySelector('input[name="link_type"][value="url"]');
                    const linkInput = document.getElementById('banner-link');
                    if (urlRadio) urlRadio.checked = true;
                    if (linkInput) linkInput.value = bannerData.link;
                    this.toggleBannerLinkInputs('url');
                }
            },

            /**
             * Нулиране на формата за банер
             */
            resetBannerForm: function() {
                if (this.banners.bannerForm) {
                    this.banners.bannerForm.reset();
                }

                // Нулиране на изображението
                this.resetBannerImagePreview();

                // Настройка на първоначалното състояние на линковете
                this.toggleBannerLinkInputs('page');
            },

            /**
             * Обновяване на прегледа на изображението за банер
             */
            updateBannerImagePreview: function(imageUrl) {
                const container = document.getElementById('banner-image-container');
                if (container) {
                    container.innerHTML = `
                        <div class="relative group" style="width: 300px; height: auto; min-height: 150px;">
                            <div class="aspect-auto rounded-lg overflow-hidden border border-gray-200">
                                <img src="${imageUrl}" alt="Banner image" class="w-full h-full object-contain">
                            </div>
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Промени изображение" data-action="select-banner-image">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-folder-image-line"></i>
                                    </div>
                                </button>
                                <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахни изображение" onclick="BackendModule.resetBannerImagePreview()">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>
                            </div>
                        </div>
                    `;
                }
            },

            /**
             * Нулиране на прегледа на изображението за банер
             */
            resetBannerImagePreview: function() {
                const container = document.getElementById('banner-image-container');
                if (container) {
                    container.innerHTML = `
                        <div id="banner-image-placeholder" class="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" style="width: 300px; height: 150px;">
                            <div class="flex items-center space-x-2 mb-2">
                                <button type="button" data-action="select-banner-image" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                                    <i class="ri-folder-image-line ri-lg"></i>
                                </button>
                            </div>
                            <p class="text-xs text-gray-400 text-center">Няма изображение</p>
                        </div>
                    `;
                }

                // Нулиране на скритото поле
                const imageInput = document.getElementById('banner-image-input');
                if (imageInput) {
                    imageInput.value = '';
                }
            },

            /**
             * Зареждане на страниците за банерите
             */
            loadBannerPages: function() {
                const self = this;
                const userToken = this.getUserToken();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/autocomplete&type=pages&user_token=' + userToken, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(pages => {
                    const select = document.getElementById('banner-page-select');

                    if (select) {
                        // Изчистване на съществуващите опции (освен първата)
                        while (select.children.length > 1) {
                            select.removeChild(select.lastChild);
                        }

                        // Добавяне на новите опции
                        pages.forEach(page => {
                            const option = document.createElement('option');
                            option.value = page.id;
                            option.textContent = page.name;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Load pages error:', error);
                });
            },

            /**
             * Запазване на банер
             */
            saveBanner: function() {
                const self = this;
                const formData = new FormData(this.banners.bannerForm);
                formData.append('tab', 'banners');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                        self.closeBannerModal();

                        // Презареждане на страницата за обновяване на списъка
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Save banner error:', error);
                    self.showAlert('error', 'Възникна грешка при запазването на банера.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Изтриване на банер
             */
            deleteBanner: function(bannerId) {
                if (!confirm('Сигурни ли сте, че искате да изтриете този банер?')) {
                    return;
                }

                const self = this;
                const formData = new FormData();
                formData.append('id', bannerId);
                formData.append('tab', 'banners');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/delete', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);

                        // Премахване на банера от DOM
                        const bannerItem = document.querySelector(`[data-banner-id="${bannerId}"]`);
                        if (bannerItem) {
                            bannerItem.remove();
                        }
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Delete banner error:', error);
                    self.showAlert('error', 'Възникна грешка при изтриването на банера.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Преглед на банер
             */
            previewBanner: function(imageUrl) {
                const previewImage = document.getElementById('banner-preview-image');
                if (previewImage && this.banners.previewModal) {
                    previewImage.src = imageUrl;
                    this.banners.previewModal.classList.remove('hidden');
                }
            },

            /**
             * Отваряне на image manager за банери
             */
            openBannerImageManager: function(type) {
                const self = this;
                const targetContainer = document.getElementById('banner-image-container');

                BackendModule.openImageManager({
                    singleSelection: true,
                    startDirectory: '',
                    target: targetContainer,
                    callback: function(selectedImages, target) {
                        if (selectedImages && selectedImages.length > 0 && target && type === 'banner') {
                            const selectedImage = selectedImages[0];
                            const imageInput = document.getElementById('banner-image-input');

                            if (imageInput) {
                                imageInput.value = selectedImage.path;
                            }

                            self.updateBannerImagePreview(selectedImage.url);
                            self.showAlert('success', 'Изображението е успешно избрано.');
                        }
                    }
                });
            },

            /**
             * Редактиране на банер
             */
            editBanner: function(bannerId) {
                // Тази функционалност трябва да се имплементира според нуждите
                console.log('Edit banner:', bannerId);
            }
        });
    }

})();
