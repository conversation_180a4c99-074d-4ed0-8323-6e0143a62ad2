<!-- CSS Styles for Tabs -->
<style>
.tab-button {
    border: none;
    background: none;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    position: relative;
}

.tab-button:hover {
    color: #6366f1 !important;
}

.tab-button.active {
    color: #6366f1 !important;
    border-bottom-color: #6366f1 !important;
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #6366f1;
    border-radius: 1px;
}

.tab-content {
    display: block;
}

.tab-content.hidden {
    display: none !important;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Ensure proper tab styling */
.tab-button.text-primary {
    color: #6366f1 !important;
}

.tab-button.text-gray-600 {
    color: #4b5563 !important;
}

.tab-button.text-gray-600:hover {
    color: #6366f1 !important;
}
</style>

<!-- Theme Page Header -->
<header class="bg-white border-b border-gray-200" style="margin-top: -2px; position: relative;">
    <div class="px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center justify-between">
            <div class="flex items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Настройки на тема</h1>
                    <p class="text-gray-500 mt-1">Персонализирайте визуалния стил на вашия онлайн магазин</p>
                </div>
            </div>
        </div>
    </div>
    <div class="flex border-b border-gray-200">
        <button id="tab-slider" class="tab-button {{ current_tab == 'slider' ? ' active text-primary' : ' text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="slider">Начален слайдер</button>
        <button id="tab-banners" class="tab-button {{ current_tab == 'banners' ? ' active text-primary' : ' text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="banners">Банери</button>
        <button id="tab-pages" class="tab-button {{ current_tab == 'pages' ? ' active text-primary' : ' text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="pages">Страници</button>
    </div>
</header>
<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto p-6 bg-gray-50">
    <!-- Tab Contents -->
    <div class="tab-contents">
        <!-- Slider Tab -->
        <div id="content-slider" class="tab-content {{ current_tab == 'slider' ? '' : ' hidden' }}">
            {% if current_tab == 'slider' %}
                {% include 'system/tabs/theme-slider.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-slideshow-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Начален слайдер</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Banners Tab -->
        <div id="content-banners" class="tab-content {{ current_tab == 'banners' ? '' : ' hidden' }}">
            {% if current_tab == 'banners' %}
                {% include 'system/tabs/theme-banners.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-image-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Банери</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Pages Tab -->
        <div id="content-pages" class="tab-content {{ current_tab == 'pages' ? '' : ' hidden' }}">
            {% if current_tab == 'pages' %}
                {% include 'system/tabs/theme-pages.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-pages-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Страници</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</main>

<!-- JavaScript Configuration -->
{{ js_config|raw }}