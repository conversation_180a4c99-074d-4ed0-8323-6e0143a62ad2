/**
 * JavaScript функционалност за управление на страници в темата
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initThemePages();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за страниците
            pages: {
                deleteModal: null,
                bulkDeleteModal: null,
                selectedPages: [],
                selectAllCheckbox: null,
                pageCheckboxes: null,
                bulkActionSelect: null,
                applyBulkActionBtn: null,
                currentPageId: null
            },

            /**
             * Инициализация на функционалността за страниците
             */
            initThemePages: function() {
                this.initPageElements();
                this.initPageEvents();
            },

            /**
             * Инициализация на елементите за страниците
             */
            initPageElements: function() {
                this.pages.deleteModal = document.getElementById('delete-page-modal');
                this.pages.bulkDeleteModal = document.getElementById('bulk-delete-modal');
                this.pages.pageModal = document.getElementById('page-modal');
                this.pages.pageForm = document.getElementById('page-form');
                this.pages.selectAllCheckbox = document.getElementById('select-all-pages');
                this.pages.pageCheckboxes = document.querySelectorAll('.page-checkbox');
                this.pages.bulkActionSelect = document.getElementById('bulk-action');
                this.pages.applyBulkActionBtn = document.getElementById('apply-bulk-action');
                this.pages.currentPageId = null;
                this.pages.richTextEditor = null;
            },

            /**
             * Свързване на събития за страниците
             */
            initPageEvents: function() {
                const self = this;

                // Избор на всички страници
                if (this.pages.selectAllCheckbox) {
                    this.pages.selectAllCheckbox.addEventListener('change', function(e) {
                        self.toggleAllPages(e.target.checked);
                    });
                }

                // Избор на отделни страници
                this.pages.pageCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        self.updateSelectedPages();
                    });
                });

                // Бутони за изтриване
                this.initPageDeleteButtons();

                // Bulk действия
                this.initPageBulkActions();

                // Затваряне на модалите
                this.initPageModalEvents();

                // Бутон за добавяне на страница
                const addPageBtn = document.getElementById('add-page-button');
                if (addPageBtn) {
                    addPageBtn.addEventListener('click', function() {
                        self.openPageModal();
                    });
                }

                // Бутони за редактиране на страници
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.edit-page')) {
                        const pageId = e.target.closest('.edit-page').getAttribute('data-page-id');
                        if (pageId) {
                            self.editPage(pageId);
                        }
                    }
                });

                // AdvancedRichTextEditor ще се инициализира при отваряне на модала
            },

            /**
             * Свързване на бутоните за изтриване на страници
             */
            initPageDeleteButtons: function() {
                const self = this;

                document.addEventListener('click', function(e) {
                    if (e.target.closest('.delete-page')) {
                        e.preventDefault();
                        const pageId = e.target.closest('.delete-page').getAttribute('data-page-id');
                        self.showPageDeleteConfirmation(pageId);
                    }
                });
            },

            /**
             * Свързване на bulk действията за страници
             */
            initPageBulkActions: function() {
                const self = this;

                if (this.pages.bulkActionSelect) {
                    this.pages.bulkActionSelect.addEventListener('change', function() {
                        self.updatePageBulkActionButton();
                    });
                }

                if (this.pages.applyBulkActionBtn) {
                    this.pages.applyBulkActionBtn.addEventListener('click', function() {
                        self.applyPageBulkAction();
                    });
                }
            },

            /**
             * Свързване на събитията за модалите на страниците
             */
            initPageModalEvents: function() {
                const self = this;

                // Delete modal
                const cancelDeleteBtn = document.getElementById('cancel-delete-page');
                const confirmDeleteBtn = document.getElementById('confirm-delete-page');

                if (cancelDeleteBtn) {
                    cancelDeleteBtn.addEventListener('click', function() {
                        self.hidePageDeleteModal();
                    });
                }

                if (confirmDeleteBtn) {
                    confirmDeleteBtn.addEventListener('click', function() {
                        self.confirmPageDelete();
                    });
                }

                // Bulk delete modal
                const cancelBulkDeleteBtn = document.getElementById('cancel-bulk-delete');
                const confirmBulkDeleteBtn = document.getElementById('confirm-bulk-delete');

                if (cancelBulkDeleteBtn) {
                    cancelBulkDeleteBtn.addEventListener('click', function() {
                        self.hidePageBulkDeleteModal();
                    });
                }

                if (confirmBulkDeleteBtn) {
                    confirmBulkDeleteBtn.addEventListener('click', function() {
                        self.confirmPageBulkDelete();
                    });
                }

                // Клик извън модалите
                if (this.pages.deleteModal) {
                    this.pages.deleteModal.addEventListener('click', function(e) {
                        if (e.target === self.pages.deleteModal) {
                            self.hidePageDeleteModal();
                        }
                    });
                }

                if (this.pages.bulkDeleteModal) {
                    this.pages.bulkDeleteModal.addEventListener('click', function(e) {
                        if (e.target === self.pages.bulkDeleteModal) {
                            self.hidePageBulkDeleteModal();
                        }
                    });
                }

                // Page edit modal
                const closePageModalBtns = document.querySelectorAll('.close-page-modal');
                closePageModalBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        self.closePageModal();
                    });
                });

                if (this.pages.pageModal) {
                    this.pages.pageModal.addEventListener('click', function(e) {
                        if (e.target === self.pages.pageModal) {
                            self.closePageModal();
                        }
                    });
                }

                // Page form submit
                if (this.pages.pageForm) {
                    this.pages.pageForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.savePage();
                    });
                }
            },

            /**
             * Превключване на всички страници
             */
            toggleAllPages: function(checked) {
                this.pages.pageCheckboxes.forEach(checkbox => {
                    checkbox.checked = checked;
                });
                this.updateSelectedPages();
            },

            /**
             * Обновяване на избраните страници
             */
            updateSelectedPages: function() {
                this.pages.selectedPages = [];
                this.pages.pageCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        this.pages.selectedPages.push(checkbox.value);
                    }
                });

                // Обновяване на "select all" checkbox
                if (this.pages.selectAllCheckbox) {
                    const allChecked = this.pages.pageCheckboxes.length > 0 &&
                                     Array.from(this.pages.pageCheckboxes).every(cb => cb.checked);
                    const someChecked = Array.from(this.pages.pageCheckboxes).some(cb => cb.checked);

                    this.pages.selectAllCheckbox.checked = allChecked;
                    this.pages.selectAllCheckbox.indeterminate = someChecked && !allChecked;
                }

                this.updatePageBulkActionButton();
            },

            /**
             * Обновяване на бутона за bulk действия за страници
             */
            updatePageBulkActionButton: function() {
                const hasSelection = this.pages.selectedPages.length > 0;
                const hasAction = this.pages.bulkActionSelect && this.pages.bulkActionSelect.value !== '';

                if (this.pages.applyBulkActionBtn) {
                    this.pages.applyBulkActionBtn.disabled = !hasSelection || !hasAction;
                }
            },

            /**
             * Показване на потвърждение за изтриване на страница
             */
            showPageDeleteConfirmation: function(pageId) {
                this.pages.currentPageId = pageId;
                if (this.pages.deleteModal) {
                    this.pages.deleteModal.classList.remove('hidden');
                }
            },

            /**
             * Скриване на модала за изтриване на страница
             */
            hidePageDeleteModal: function() {
                if (this.pages.deleteModal) {
                    this.pages.deleteModal.classList.add('hidden');
                }
                this.pages.currentPageId = null;
            },

            /**
             * Потвърждаване на изтриването на страница
             */
            confirmPageDelete: function() {
                if (!this.pages.currentPageId) return;

                const self = this;
                const formData = new FormData();
                formData.append('id', this.pages.currentPageId);
                formData.append('tab', 'pages');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/delete', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                        self.hidePageDeleteModal();

                        // Премахване на реда от таблицата
                        const row = document.querySelector(`input[value="${self.pages.currentPageId}"]`);
                        if (row) {
                            const tableRow = row.closest('tr');
                            if (tableRow) {
                                tableRow.remove();
                            }
                        }

                        self.updateSelectedPages();
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Delete page error:', error);
                    self.showAlert('error', 'Възникна грешка при изтриването на страницата.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Прилагане на bulk действие за страници
             */
            applyPageBulkAction: function() {
                if (this.pages.selectedPages.length === 0) {
                    this.showAlert('error', 'Моля, изберете поне една страница.');
                    return;
                }

                const action = this.pages.bulkActionSelect.value;

                switch (action) {
                    case 'delete':
                        this.showPageBulkDeleteConfirmation();
                        break;
                    case 'activate':
                        this.bulkUpdatePageStatus(1);
                        break;
                    case 'deactivate':
                        this.bulkUpdatePageStatus(0);
                        break;
                    default:
                        this.showAlert('error', 'Моля, изберете действие.');
                }
            },

            /**
             * Показване на потвърждение за bulk изтриване на страници
             */
            showPageBulkDeleteConfirmation: function() {
                if (this.pages.bulkDeleteModal) {
                    this.pages.bulkDeleteModal.classList.remove('hidden');
                }
            },

            /**
             * Скриване на модала за bulk изтриване на страници
             */
            hidePageBulkDeleteModal: function() {
                if (this.pages.bulkDeleteModal) {
                    this.pages.bulkDeleteModal.classList.add('hidden');
                }
            },

            /**
             * Потвърждаване на bulk изтриването на страници
             */
            confirmPageBulkDelete: function() {
                const self = this;
                const formData = new FormData();
                formData.append('ids', JSON.stringify(this.pages.selectedPages));
                formData.append('action', 'bulk_delete');
                formData.append('tab', 'pages');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/delete', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                        self.hidePageBulkDeleteModal();

                        // Премахване на редовете от таблицата
                        self.pages.selectedPages.forEach(pageId => {
                            const row = document.querySelector(`input[value="${pageId}"]`);
                            if (row) {
                                const tableRow = row.closest('tr');
                                if (tableRow) {
                                    tableRow.remove();
                                }
                            }
                        });

                        self.pages.selectedPages = [];
                        self.updateSelectedPages();

                        // Нулиране на bulk action select
                        if (self.pages.bulkActionSelect) {
                            self.pages.bulkActionSelect.value = '';
                        }
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Bulk delete error:', error);
                    self.showAlert('error', 'Възникна грешка при изтриването на страниците.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Bulk обновяване на статуса на страници
             */
            bulkUpdatePageStatus: function(status) {
                const self = this;
                const formData = new FormData();
                formData.append('ids', JSON.stringify(this.pages.selectedPages));
                formData.append('status', status);
                formData.append('action', 'bulk_status');
                formData.append('tab', 'pages');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);

                        // Обновяване на статуса в таблицата
                        self.pages.selectedPages.forEach(pageId => {
                            const row = document.querySelector(`input[value="${pageId}"]`);
                            if (row) {
                                const tableRow = row.closest('tr');
                                if (tableRow) {
                                    const statusCell = tableRow.querySelector('td:nth-child(3) span');
                                    if (statusCell) {
                                        if (status === 1) {
                                            statusCell.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
                                            statusCell.textContent = 'Активна';
                                        } else {
                                            statusCell.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
                                            statusCell.textContent = 'Неактивна';
                                        }
                                    }
                                }
                            }
                        });

                        // Нулиране на избора
                        self.pages.selectedPages = [];
                        self.pages.pageCheckboxes.forEach(cb => cb.checked = false);
                        self.updateSelectedPages();

                        // Нулиране на bulk action select
                        if (self.pages.bulkActionSelect) {
                            self.pages.bulkActionSelect.value = '';
                        }
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Bulk status update error:', error);
                    self.showAlert('error', 'Възникна грешка при обновяването на статуса.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Инициализация на AdvancedRichTextEditor
             */
            initRichTextEditor: function() {
                // Проверка дали вече е инициализиран
                if (this.pages.richTextEditor) {
                    return;
                }

                // Проверка дали AdvancedRichTextEditor е наличен
                if (typeof AdvancedRichTextEditor !== 'undefined') {
                    const editorContainer = document.getElementById('page-content-editor');
                    const textarea = document.getElementById('page-description');

                    if (editorContainer && textarea) {
                        try {
                            this.pages.richTextEditor = new AdvancedRichTextEditor({
                                container: editorContainer,
                                textarea: textarea,
                                height: 300,
                                toolbar: [
                                    'bold', 'italic', 'underline', '|',
                                    'h1', 'h2', 'h3', '|',
                                    'ul', 'ol', '|',
                                    'link', 'image', '|',
                                    'align-left', 'align-center', 'align-right', '|',
                                    'code', 'quote', '|',
                                    'undo', 'redo'
                                ]
                            });
                        } catch (error) {
                            console.error('Грешка при инициализация на AdvancedRichTextEditor:', error);
                        }
                    } else {
                        console.warn('Контейнерът или textarea за AdvancedRichTextEditor не са намерени');
                    }
                } else {
                    console.warn('AdvancedRichTextEditor не е зареден');
                }
            },

            /**
             * Отваряне на модала за страница
             */
            openPageModal: function(pageData) {
                pageData = pageData || null;
                this.pages.currentPageId = pageData ? pageData.information_id : null;

                // Задаване на заглавието
                const title = document.getElementById('page-modal-title');
                if (title) {
                    title.textContent = pageData ? 'Редактиране на страница' : 'Добавяне на страница';
                }

                // Попълване на формата
                if (pageData) {
                    this.populatePageForm(pageData);
                } else {
                    this.resetPageForm();
                }

                // Показване на модала
                if (this.pages.pageModal) {
                    this.pages.pageModal.classList.remove('hidden');

                    // Инициализация на AdvancedRichTextEditor след показване на модала
                    setTimeout(() => {
                        this.initRichTextEditor();
                    }, 100); // Малко забавяне за да се рендира модалът
                }
            },

            /**
             * Затваряне на модала за страница
             */
            closePageModal: function() {
                if (this.pages.pageModal) {
                    this.pages.pageModal.classList.add('hidden');
                }
                this.pages.currentPageId = null;
                this.resetPageForm();
            },

            /**
             * Попълване на формата за страница
             */
            populatePageForm: function(pageData) {
                const fields = {
                    'page-id': pageData.information_id || '',
                    'page-title': pageData.title || '',
                    'page-keyword': pageData.keyword || '',
                    'page-meta-title': pageData.meta_title || '',
                    'page-meta-keyword': pageData.meta_keyword || '',
                    'page-meta-description': pageData.meta_description || ''
                };

                // Попълване на полетата
                Object.keys(fields).forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        field.value = fields[fieldId];
                    }
                });

                // Статус checkbox
                const statusField = document.getElementById('page-status');
                if (statusField) {
                    statusField.checked = pageData.status || false;
                }

                // Съдържание в rich text editor
                if (this.pages.richTextEditor && pageData.description) {
                    this.pages.richTextEditor.setContent(pageData.description);
                } else {
                    const descriptionField = document.getElementById('page-description');
                    if (descriptionField) {
                        descriptionField.value = pageData.description || '';
                    }
                }
            },

            /**
             * Нулиране на формата за страница
             */
            resetPageForm: function() {
                if (this.pages.pageForm) {
                    this.pages.pageForm.reset();
                }

                // Изчистване на rich text editor
                if (this.pages.richTextEditor) {
                    this.pages.richTextEditor.setContent('');
                }
            },

            /**
             * Редактиране на страница
             */
            editPage: function(pageId) {
                const self = this;

                if (!pageId) {
                    self.showAlert('error', 'Невалиден ID на страница.');
                    return;
                }

                // Показване на loading индикатор
                this.showThemeLoading();

                // AJAX заявка за получаване на данните на страницата
                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/page_edit&id=' + pageId + '&user_token=' + this.getUserToken(), {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(pageData => {
                    if (pageData && pageData.information_id) {
                        // Отваряне на модала с данните на страницата
                        self.openPageModal(pageData);
                    } else {
                        self.showAlert('error', 'Страницата не е намерена или възникна грешка при зареждането.');
                    }
                })
                .catch(error => {
                    console.error('Edit page error:', error);
                    self.showAlert('error', 'Възникна грешка при зареждането на данните на страницата.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Запазване на страница
             */
            savePage: function() {
                const self = this;
                const formData = new FormData(this.pages.pageForm);

                // Добавяне на съдържанието от rich text editor
                if (this.pages.richTextEditor) {
                    const content = this.pages.richTextEditor.getContent();
                    formData.set('description', content);
                }

                formData.append('action', this.pages.currentPageId ? 'edit' : 'add');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/page_save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                        self.closePageModal();

                        // Презареждане на страницата за да се покажат промените
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Save page error:', error);
                    self.showAlert('error', 'Възникна грешка при запазването на страницата.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            }
        });
    }

})();
