/**
 * JavaScript функционалност за управление на страници в темата
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initThemePages();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за страниците
            pages: {
                deleteModal: null,
                bulkDeleteModal: null,
                selectedPages: [],
                selectAllCheckbox: null,
                pageCheckboxes: null,
                bulkActionSelect: null,
                applyBulkActionBtn: null,
                currentPageId: null
            },

            /**
             * Инициализация на функционалността за страниците
             */
            initThemePages: function() {
                this.initPageElements();
                this.initPageEvents();
            },

            /**
             * Инициализация на елементите за страниците
             */
            initPageElements: function() {
                this.pages.deleteModal = document.getElementById('delete-page-modal');
                this.pages.bulkDeleteModal = document.getElementById('bulk-delete-modal');
                this.pages.selectAllCheckbox = document.getElementById('select-all-pages');
                this.pages.pageCheckboxes = document.querySelectorAll('.page-checkbox');
                this.pages.bulkActionSelect = document.getElementById('bulk-action');
                this.pages.applyBulkActionBtn = document.getElementById('apply-bulk-action');
            },

            /**
             * Свързване на събития за страниците
             */
            initPageEvents: function() {
                const self = this;

                // Избор на всички страници
                if (this.pages.selectAllCheckbox) {
                    this.pages.selectAllCheckbox.addEventListener('change', function(e) {
                        self.toggleAllPages(e.target.checked);
                    });
                }

                // Избор на отделни страници
                this.pages.pageCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        self.updateSelectedPages();
                    });
                });

                // Бутони за изтриване
                this.initPageDeleteButtons();

                // Bulk действия
                this.initPageBulkActions();

                // Затваряне на модалите
                this.initPageModalEvents();
            },

            /**
             * Свързване на бутоните за изтриване на страници
             */
            initPageDeleteButtons: function() {
                const self = this;

                document.addEventListener('click', function(e) {
                    if (e.target.closest('.delete-page')) {
                        e.preventDefault();
                        const pageId = e.target.closest('.delete-page').getAttribute('data-page-id');
                        self.showPageDeleteConfirmation(pageId);
                    }
                });
            },

            /**
             * Свързване на bulk действията за страници
             */
            initPageBulkActions: function() {
                const self = this;

                if (this.pages.bulkActionSelect) {
                    this.pages.bulkActionSelect.addEventListener('change', function() {
                        self.updatePageBulkActionButton();
                    });
                }

                if (this.pages.applyBulkActionBtn) {
                    this.pages.applyBulkActionBtn.addEventListener('click', function() {
                        self.applyPageBulkAction();
                    });
                }
            },

            /**
             * Свързване на събитията за модалите на страниците
             */
            initPageModalEvents: function() {
                const self = this;

                // Delete modal
                const cancelDeleteBtn = document.getElementById('cancel-delete-page');
                const confirmDeleteBtn = document.getElementById('confirm-delete-page');

                if (cancelDeleteBtn) {
                    cancelDeleteBtn.addEventListener('click', function() {
                        self.hidePageDeleteModal();
                    });
                }

                if (confirmDeleteBtn) {
                    confirmDeleteBtn.addEventListener('click', function() {
                        self.confirmPageDelete();
                    });
                }

                // Bulk delete modal
                const cancelBulkDeleteBtn = document.getElementById('cancel-bulk-delete');
                const confirmBulkDeleteBtn = document.getElementById('confirm-bulk-delete');

                if (cancelBulkDeleteBtn) {
                    cancelBulkDeleteBtn.addEventListener('click', function() {
                        self.hidePageBulkDeleteModal();
                    });
                }

                if (confirmBulkDeleteBtn) {
                    confirmBulkDeleteBtn.addEventListener('click', function() {
                        self.confirmPageBulkDelete();
                    });
                }

                // Клик извън модалите
                if (this.pages.deleteModal) {
                    this.pages.deleteModal.addEventListener('click', function(e) {
                        if (e.target === self.pages.deleteModal) {
                            self.hidePageDeleteModal();
                        }
                    });
                }

                if (this.pages.bulkDeleteModal) {
                    this.pages.bulkDeleteModal.addEventListener('click', function(e) {
                        if (e.target === self.pages.bulkDeleteModal) {
                            self.hidePageBulkDeleteModal();
                        }
                    });
                }
            },

            /**
             * Превключване на всички страници
             */
            toggleAllPages: function(checked) {
                this.pages.pageCheckboxes.forEach(checkbox => {
                    checkbox.checked = checked;
                });
                this.updateSelectedPages();
            },

            /**
             * Обновяване на избраните страници
             */
            updateSelectedPages: function() {
                this.pages.selectedPages = [];
                this.pages.pageCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        this.pages.selectedPages.push(checkbox.value);
                    }
                });

                // Обновяване на "select all" checkbox
                if (this.pages.selectAllCheckbox) {
                    const allChecked = this.pages.pageCheckboxes.length > 0 &&
                                     Array.from(this.pages.pageCheckboxes).every(cb => cb.checked);
                    const someChecked = Array.from(this.pages.pageCheckboxes).some(cb => cb.checked);

                    this.pages.selectAllCheckbox.checked = allChecked;
                    this.pages.selectAllCheckbox.indeterminate = someChecked && !allChecked;
                }

                this.updatePageBulkActionButton();
            },

            /**
             * Обновяване на бутона за bulk действия за страници
             */
            updatePageBulkActionButton: function() {
                const hasSelection = this.pages.selectedPages.length > 0;
                const hasAction = this.pages.bulkActionSelect && this.pages.bulkActionSelect.value !== '';

                if (this.pages.applyBulkActionBtn) {
                    this.pages.applyBulkActionBtn.disabled = !hasSelection || !hasAction;
                }
            },

            /**
             * Показване на потвърждение за изтриване на страница
             */
            showPageDeleteConfirmation: function(pageId) {
                this.pages.currentPageId = pageId;
                if (this.pages.deleteModal) {
                    this.pages.deleteModal.classList.remove('hidden');
                }
            },

            /**
             * Скриване на модала за изтриване на страница
             */
            hidePageDeleteModal: function() {
                if (this.pages.deleteModal) {
                    this.pages.deleteModal.classList.add('hidden');
                }
                this.pages.currentPageId = null;
            },

            /**
             * Потвърждаване на изтриването на страница
             */
            confirmPageDelete: function() {
                if (!this.pages.currentPageId) return;

                const self = this;
                const formData = new FormData();
                formData.append('id', this.pages.currentPageId);
                formData.append('tab', 'pages');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/delete', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                        self.hidePageDeleteModal();

                        // Премахване на реда от таблицата
                        const row = document.querySelector(`input[value="${self.pages.currentPageId}"]`);
                        if (row) {
                            const tableRow = row.closest('tr');
                            if (tableRow) {
                                tableRow.remove();
                            }
                        }

                        self.updateSelectedPages();
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Delete page error:', error);
                    self.showAlert('error', 'Възникна грешка при изтриването на страницата.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Прилагане на bulk действие за страници
             */
            applyPageBulkAction: function() {
                if (this.pages.selectedPages.length === 0) {
                    this.showAlert('error', 'Моля, изберете поне една страница.');
                    return;
                }

                const action = this.pages.bulkActionSelect.value;

                switch (action) {
                    case 'delete':
                        this.showPageBulkDeleteConfirmation();
                        break;
                    case 'activate':
                        this.bulkUpdatePageStatus(1);
                        break;
                    case 'deactivate':
                        this.bulkUpdatePageStatus(0);
                        break;
                    default:
                        this.showAlert('error', 'Моля, изберете действие.');
                }
            },

            /**
             * Показване на потвърждение за bulk изтриване на страници
             */
            showPageBulkDeleteConfirmation: function() {
                if (this.pages.bulkDeleteModal) {
                    this.pages.bulkDeleteModal.classList.remove('hidden');
                }
            },

            /**
             * Скриване на модала за bulk изтриване на страници
             */
            hidePageBulkDeleteModal: function() {
                if (this.pages.bulkDeleteModal) {
                    this.pages.bulkDeleteModal.classList.add('hidden');
                }
            },

            /**
             * Потвърждаване на bulk изтриването на страници
             */
            confirmPageBulkDelete: function() {
                const self = this;
                const formData = new FormData();
                formData.append('ids', JSON.stringify(this.pages.selectedPages));
                formData.append('action', 'bulk_delete');
                formData.append('tab', 'pages');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/delete', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);
                        self.hidePageBulkDeleteModal();

                        // Премахване на редовете от таблицата
                        self.pages.selectedPages.forEach(pageId => {
                            const row = document.querySelector(`input[value="${pageId}"]`);
                            if (row) {
                                const tableRow = row.closest('tr');
                                if (tableRow) {
                                    tableRow.remove();
                                }
                            }
                        });

                        self.pages.selectedPages = [];
                        self.updateSelectedPages();

                        // Нулиране на bulk action select
                        if (self.pages.bulkActionSelect) {
                            self.pages.bulkActionSelect.value = '';
                        }
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Bulk delete error:', error);
                    self.showAlert('error', 'Възникна грешка при изтриването на страниците.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            },

            /**
             * Bulk обновяване на статуса на страници
             */
            bulkUpdatePageStatus: function(status) {
                const self = this;
                const formData = new FormData();
                formData.append('ids', JSON.stringify(this.pages.selectedPages));
                formData.append('status', status);
                formData.append('action', 'bulk_status');
                formData.append('tab', 'pages');
                formData.append('user_token', this.getUserToken());

                // Показване на loading индикатор
                this.showThemeLoading();

                fetch(window.location.href.replace(/&tab=\w+/, '') + '&route=system/theme/save', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        self.showAlert('success', result.message);

                        // Обновяване на статуса в таблицата
                        self.pages.selectedPages.forEach(pageId => {
                            const row = document.querySelector(`input[value="${pageId}"]`);
                            if (row) {
                                const tableRow = row.closest('tr');
                                if (tableRow) {
                                    const statusCell = tableRow.querySelector('td:nth-child(3) span');
                                    if (statusCell) {
                                        if (status === 1) {
                                            statusCell.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
                                            statusCell.textContent = 'Активна';
                                        } else {
                                            statusCell.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
                                            statusCell.textContent = 'Неактивна';
                                        }
                                    }
                                }
                            }
                        });

                        // Нулиране на избора
                        self.pages.selectedPages = [];
                        self.pages.pageCheckboxes.forEach(cb => cb.checked = false);
                        self.updateSelectedPages();

                        // Нулиране на bulk action select
                        if (self.pages.bulkActionSelect) {
                            self.pages.bulkActionSelect.value = '';
                        }
                    } else {
                        self.showAlert('error', result.message);
                    }
                })
                .catch(error => {
                    console.error('Bulk status update error:', error);
                    self.showAlert('error', 'Възникна грешка при обновяването на статуса.');
                })
                .finally(() => {
                    self.hideThemeLoading();
                });
            }
        });
    }

})();
